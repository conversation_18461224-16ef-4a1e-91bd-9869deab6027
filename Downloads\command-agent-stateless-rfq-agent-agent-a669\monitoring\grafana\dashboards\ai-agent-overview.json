{"dashboard": {"id": null, "title": "LangGraph AI Agent System Overview", "tags": ["ai-agent", "langgraph", "rfq", "12-factor"], "timezone": "browser", "schemaVersion": 36, "version": 1, "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Health Overview", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "up{job='ai-agent-service'}", "legendFormat": "Agent Service", "refId": "A"}, {"expr": "up{job='postgresql'}", "legendFormat": "PostgreSQL", "refId": "B"}, {"expr": "up{job='redis'}", "legendFormat": "Redis", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "unit": "none"}}}, {"id": 2, "title": "RFQ Workflow Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "rate(langgraph_workflow_completions_total[5m])", "legendFormat": "Workflow Completions/sec", "refId": "A"}, {"expr": "rate(langgraph_workflow_failures_total[5m])", "legendFormat": "Workflow Failures/sec", "refId": "B"}], "yAxes": [{"label": "Operations/sec", "min": 0}]}, {"id": 3, "title": "Agent Response Times", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job='ai-agent-service'}[5m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job='ai-agent-service'}[5m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job='ai-agent-service'}[5m]))", "legendFormat": "99th percentile", "refId": "C"}], "yAxes": [{"label": "Seconds", "min": 0}]}, {"id": 4, "title": "Memory Service Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "rate(memory_searches_total[5m])", "legendFormat": "Memory Searches/sec", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(memory_search_duration_seconds_bucket[5m]))", "legendFormat": "Search Duration 95th percentile", "refId": "B"}, {"expr": "memory_consolidation_operations_total", "legendFormat": "Total Consolidations", "refId": "C"}]}, {"id": 5, "title": "Vector Database Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "rate(pgvector_searches_total[5m])", "legendFormat": "Vector Searches/sec", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(pgvector_search_duration_seconds_bucket[5m]))", "legendFormat": "Vector Search Duration 95th percentile", "refId": "B"}, {"expr": "pgvector_index_size_bytes", "legendFormat": "Vector Index Size", "refId": "C"}]}, {"id": 6, "title": "Multi-Agent Orchestration", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "agent_executions_total{agent_type='market_research'}", "legendFormat": "Market Research Agent", "refId": "A"}, {"expr": "agent_executions_total{agent_type='vendor_discovery'}", "legendFormat": "Vendor Discovery Agent", "refId": "B"}, {"expr": "agent_executions_total{agent_type='document_generation'}", "legendFormat": "Document Generation Agent", "refId": "C"}]}, {"id": 7, "title": "Input Gateway Activity", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "rate(input_requests_total{source='api'}[5m])", "legendFormat": "API Requests", "refId": "A"}, {"expr": "rate(input_requests_total{source='email'}[5m])", "legendFormat": "Email Inputs", "refId": "B"}, {"expr": "rate(input_requests_total{source='chat'}[5m])", "legendFormat": "Chat Inputs", "refId": "C"}, {"expr": "rate(input_requests_total{source='webhook'}[5m])", "legendFormat": "Webhook Inputs", "refId": "D"}]}, {"id": 8, "title": "Error Analysis", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "rate(error_manager_compactions_total[5m])", "legendFormat": "Error Compactions/sec", "refId": "A"}, {"expr": "error_manager_pattern_matches_total", "legendFormat": "Pattern Matches", "refId": "B"}, {"expr": "rate(http_requests_total{status=~'5..'}[5m])", "legendFormat": "HTTP 5xx Errors/sec", "refId": "C"}]}, {"id": 9, "title": "Human-in-the-Loop Tasks", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 0, "y": 32}, "targets": [{"expr": "human_tasks_pending_total", "legendFormat": "Pending Tasks", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}}, {"id": 10, "title": "Resource Utilization", "type": "graph", "gridPos": {"h": 8, "w": 18, "x": 6, "y": 32}, "targets": [{"expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode='idle'}[5m])) * 100)", "legendFormat": "CPU Usage %", "refId": "A"}, {"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "Memory Usage %", "refId": "B"}, {"expr": "(1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100", "legendFormat": "Disk Usage %", "refId": "C"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}]}]}}