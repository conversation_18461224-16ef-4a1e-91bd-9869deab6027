# deploy.ps1 - PowerShell deployment script for AI Agent System on Windows
# Implements comprehensive deployment with monitoring and validation

param(
    [Parameter(Position=0)]
    [string]$Command = "deploy",
    
    [Parameter(Position=1)]
    [string]$ServiceName = ""
)

# Configuration
$ComposeProjectName = "ai-agents"
$BackupDir = "./backups/$(Get-Date -Format 'yyyyMMdd_HHmmss')"
$LogFile = "./logs/deployment_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

# Create log directory
New-Item -ItemType Directory -Force -Path "logs" | Out-Null

# Logging functions
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $Level`: $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage -ForegroundColor Cyan }
    }
    
    Add-Content -Path $LogFile -Value $logMessage
}

# Check prerequisites
function Test-Prerequisites {
    Write-Log "Checking prerequisites..."
    
    # Check Docker
    try {
        docker --version | Out-Null
        Write-Log "Docker is installed" "SUCCESS"
    }
    catch {
        Write-Log "Docker is not installed or not in PATH" "ERROR"
        exit 1
    }
    
    # Check Docker Compose
    try {
        docker-compose --version | Out-Null
        Write-Log "Docker Compose is installed" "SUCCESS"
    }
    catch {
        Write-Log "Docker Compose is not installed or not in PATH" "ERROR"
        exit 1
    }
    
    # Check available disk space (minimum 10GB)
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB
    if ($freeSpace -lt 10) {
        Write-Log "Less than 10GB disk space available on C: drive" "WARNING"
    }
    else {
        Write-Log "Sufficient disk space available: $([math]::Round($freeSpace, 2)) GB" "SUCCESS"
    }
    
    # Check environment file
    if (-not (Test-Path ".env.production")) {
        Write-Log ".env.production file not found" "ERROR"
        exit 1
    }
    
    Write-Log "Prerequisites check completed" "SUCCESS"
}

# Create required directories
function New-RequiredDirectories {
    Write-Log "Creating required directories..."
    
    $directories = @(
        "logs/agent",
        "logs/orchestrator", 
        "logs/memory",
        "logs/nginx",
        "logs/postgres",
        "logs/redis",
        "secrets",
        "monitoring/prometheus",
        "monitoring/grafana/dashboards",
        "monitoring/grafana/datasources",
        "monitoring/fluentd/conf",
        "database/backups",
        "models",
        "ssl",
        "scripts",
        "config",
        "nginx",
        "redis",
        "rabbitmq"
    )
    
    foreach ($dir in $directories) {
        New-Item -ItemType Directory -Force -Path $dir | Out-Null
        Write-Log "Created directory: $dir"
    }
    
    Write-Log "Directory creation completed" "SUCCESS"
}

# Generate secrets
function New-Secrets {
    Write-Log "Generating secrets..."
    
    # Function to generate random password
    function New-RandomPassword {
        param([int]$Length = 32)
        return [System.Convert]::ToBase64String([System.Security.Cryptography.RNGCryptoServiceProvider]::new().GetBytes($Length))
    }
    
    # Generate passwords if they don't exist
    if (-not (Test-Path "secrets/postgres_password.txt")) {
        New-RandomPassword | Out-File -FilePath "secrets/postgres_password.txt" -Encoding UTF8
        Write-Log "Generated PostgreSQL password"
    }
    
    if (-not (Test-Path "secrets/redis_password.txt")) {
        New-RandomPassword | Out-File -FilePath "secrets/redis_password.txt" -Encoding UTF8
        Write-Log "Generated Redis password"
    }
    
    if (-not (Test-Path "secrets/rabbitmq_password.txt")) {
        New-RandomPassword | Out-File -FilePath "secrets/rabbitmq_password.txt" -Encoding UTF8
        Write-Log "Generated RabbitMQ password"
    }
    
    if (-not (Test-Path "secrets/grafana_password.txt")) {
        New-RandomPassword | Out-File -FilePath "secrets/grafana_password.txt" -Encoding UTF8
        Write-Log "Generated Grafana password"
    }
    
    if (-not (Test-Path "secrets/jwt_secret.txt")) {
        New-RandomPassword -Length 64 | Out-File -FilePath "secrets/jwt_secret.txt" -Encoding UTF8
        Write-Log "Generated JWT secret"
    }
    
    Write-Log "Secret generation completed" "SUCCESS"
}

# Setup environment variables
function Set-Environment {
    Write-Log "Setting up environment variables..."
    
    # Copy production environment template
    if (-not (Test-Path ".env")) {
        Copy-Item ".env.production" ".env"
        Write-Log "Created .env from production template"
    }
    
    # Set build-time variables
    $env:BUILD_DATE = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
    $env:COMPOSE_PROJECT_NAME = $ComposeProjectName
    
    try {
        $env:GIT_COMMIT = git rev-parse HEAD
    }
    catch {
        $env:GIT_COMMIT = "unknown"
    }
    
    # Load secrets into environment
    $env:POSTGRES_PASSWORD = Get-Content "secrets/postgres_password.txt" -Raw
    $env:REDIS_PASSWORD = Get-Content "secrets/redis_password.txt" -Raw
    $env:RABBITMQ_PASSWORD = Get-Content "secrets/rabbitmq_password.txt" -Raw
    $env:GRAFANA_PASSWORD = Get-Content "secrets/grafana_password.txt" -Raw
    $env:JWT_SECRET = Get-Content "secrets/jwt_secret.txt" -Raw
    
    Write-Log "BUILD_DATE: $($env:BUILD_DATE)"
    Write-Log "GIT_COMMIT: $($env:GIT_COMMIT)"
    
    Write-Log "Environment setup completed" "SUCCESS"
}

# Create configuration files
function New-ConfigFiles {
    Write-Log "Creating configuration files..."
    
    # Nginx configuration
    $nginxConfig = @'
events {
    worker_connections 1024;
}

http {
    upstream agent_backend {
        least_conn;
        server agent-service:8000 max_fails=3 fail_timeout=30s;
        server langgraph-orchestrator:8002 max_fails=3 fail_timeout=30s backup;
    }

    upstream memory_backend {
        server memory-service:8003 max_fails=3 fail_timeout=30s;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=memory:10m rate=5r/s;

    server {
        listen 80;
        server_name _;

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://agent_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Memory service routes
        location /memory/ {
            limit_req zone=memory burst=10 nodelay;
            proxy_pass http://memory_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
'@

    $nginxConfig | Out-File -FilePath "nginx/nginx.conf" -Encoding UTF8

    # Prometheus configuration
    $prometheusConfig = @'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'agent-service'
    static_configs:
      - targets: ['agent-service:8001']
    scrape_interval: 10s
    metrics_path: /metrics
    
  - job_name: 'memory-service'
    static_configs:
      - targets: ['memory-service:8003']
    scrape_interval: 10s
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
'@

    $prometheusConfig | Out-File -FilePath "monitoring/prometheus.yml" -Encoding UTF8

    # Redis configuration
    $redisConfig = @'
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
'@

    $redisConfig | Out-File -FilePath "redis/redis.conf" -Encoding UTF8

    Write-Log "Configuration files created" "SUCCESS"
}

# Build Docker images
function Build-Images {
    Write-Log "Building Docker images..."
    
    try {
        docker-compose build --build-arg BUILD_DATE=$env:BUILD_DATE --build-arg GIT_COMMIT=$env:GIT_COMMIT --parallel
        Write-Log "Docker image build completed" "SUCCESS"
    }
    catch {
        Write-Log "Docker image build failed: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Deploy services
function Deploy-Services {
    Write-Log "Deploying services..."
    
    try {
        # Start core infrastructure first
        Write-Log "Starting database and cache services..."
        docker-compose up -d postgres redis rabbitmq
        
        # Wait for services to be healthy
        Write-Log "Waiting for services to be ready..."
        
        $timeout = 60
        $elapsed = 0
        do {
            Start-Sleep -Seconds 2
            $elapsed += 2
            $pgReady = docker-compose exec postgres pg_isready -U agent_user -d agent_db 2>$null
        } while ($LASTEXITCODE -ne 0 -and $elapsed -lt $timeout)
        
        if ($elapsed -ge $timeout) {
            Write-Log "PostgreSQL startup timeout" "ERROR"
            exit 1
        }
        
        Write-Log "PostgreSQL is ready" "SUCCESS"
        
        # Start application services
        Write-Log "Starting application services..."
        docker-compose up -d agent-service langgraph-orchestrator memory-service
        
        # Wait for application services
        Write-Log "Waiting for application services..."
        $timeout = 120
        $elapsed = 0
        do {
            Start-Sleep -Seconds 5
            $elapsed += 5
            try {
                Invoke-WebRequest -Uri "http://localhost:8001/health" -UseBasicParsing | Out-Null
                $appReady = $true
            }
            catch {
                $appReady = $false
            }
        } while (-not $appReady -and $elapsed -lt $timeout)
        
        if (-not $appReady) {
            Write-Log "Application services startup timeout" "ERROR"
            exit 1
        }
        
        Write-Log "Application services are ready" "SUCCESS"
        
        # Start supporting services
        Write-Log "Starting supporting services..."
        docker-compose up -d celery-worker nginx prometheus grafana
        
        Write-Log "Service deployment completed" "SUCCESS"
    }
    catch {
        Write-Log "Service deployment failed: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Run database migrations
function Invoke-Migrations {
    Write-Log "Running database migrations..."
    
    try {
        Start-Sleep -Seconds 10
        docker-compose exec postgres psql -U agent_user -d agent_db -f /docker-entrypoint-initdb.d/01_init_schema.sql
        Write-Log "Database migrations completed" "SUCCESS"
    }
    catch {
        Write-Log "Database migrations may have issues, but continuing..." "WARNING"
    }
}

# Validate deployment
function Test-Deployment {
    Write-Log "Validating deployment..."
    
    # Check service health
    $services = @("postgres", "redis", "agent-service", "langgraph-orchestrator", "memory-service", "nginx")
    
    foreach ($service in $services) {
        $status = docker-compose ps $service
        if ($status -match "Up") {
            Write-Log "✓ $service is running" "SUCCESS"
        }
        else {
            Write-Log "✗ $service is not running" "ERROR"
            return $false
        }
    }
    
    # Test API endpoints
    Write-Log "Testing API endpoints..."
    
    try {
        Invoke-WebRequest -Uri "http://localhost/health" -UseBasicParsing | Out-Null
        Write-Log "✓ Main API health check passed" "SUCCESS"
    }
    catch {
        Write-Log "✗ Main API health check failed" "ERROR"
        return $false
    }
    
    try {
        Invoke-WebRequest -Uri "http://localhost:8001/health" -UseBasicParsing | Out-Null
        Write-Log "✓ Agent service health check passed" "SUCCESS"
    }
    catch {
        Write-Log "✗ Agent service health check failed" "ERROR"
        return $false
    }
    
    Write-Log "Deployment validation completed" "SUCCESS"
    return $true
}

# Display deployment information
function Show-DeploymentInfo {
    Write-Host ""
    Write-Host "==========================================" -ForegroundColor Green
    Write-Host "         AI AGENT SYSTEM DEPLOYED" -ForegroundColor Green
    Write-Host "==========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Services:" -ForegroundColor Cyan
    Write-Host "  • Main API: http://localhost/api" -ForegroundColor White
    Write-Host "  • Agent Service: http://localhost:8000" -ForegroundColor White
    Write-Host "  • Memory Service: http://localhost:8003" -ForegroundColor White
    Write-Host "  • Grafana Dashboard: http://localhost:3000" -ForegroundColor White
    Write-Host "  • Prometheus Metrics: http://localhost:9090" -ForegroundColor White
    Write-Host "  • RabbitMQ Management: http://localhost:15672" -ForegroundColor White
    Write-Host ""
    Write-Host "Credentials:" -ForegroundColor Cyan
    Write-Host "  • Grafana: admin / $(Get-Content 'secrets/grafana_password.txt' -Raw)" -ForegroundColor White
    Write-Host "  • RabbitMQ: agent_user / $(Get-Content 'secrets/rabbitmq_password.txt' -Raw)" -ForegroundColor White
    Write-Host ""
    Write-Host "Management:" -ForegroundColor Cyan
    Write-Host "  • View status: docker-compose ps" -ForegroundColor White
    Write-Host "  • Stop services: docker-compose down" -ForegroundColor White
    Write-Host "  • View logs: docker-compose logs -f" -ForegroundColor White
    Write-Host ""
    Write-Host "Deployment log: $LogFile" -ForegroundColor Yellow
    Write-Host "==========================================" -ForegroundColor Green
}

# Main deployment function
function Invoke-Deployment {
    Write-Log "Starting AI Agent System deployment..." "SUCCESS"
    
    Test-Prerequisites
    New-RequiredDirectories
    New-Secrets
    Set-Environment
    New-ConfigFiles
    Build-Images
    Deploy-Services
    Invoke-Migrations
    
    if (Test-Deployment) {
        Show-DeploymentInfo
        Write-Log "Deployment completed successfully!" "SUCCESS"
    }
    else {
        Write-Log "Deployment validation failed" "ERROR"
        exit 1
    }
}

# Handle commands
switch ($Command) {
    "deploy" {
        Invoke-Deployment
    }
    "stop" {
        Write-Log "Stopping AI Agent System..."
        docker-compose down
        Write-Log "System stopped" "SUCCESS"
    }
    "restart" {
        Write-Log "Restarting AI Agent System..."
        docker-compose restart
        Write-Log "System restarted" "SUCCESS"
    }
    "logs" {
        if ($ServiceName) {
            docker-compose logs -f $ServiceName
        }
        else {
            docker-compose logs -f
        }
    }
    "status" {
        docker-compose ps
    }
    "help" {
        Write-Host "Usage: .\deploy.ps1 [command] [service_name]" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Commands:" -ForegroundColor Yellow
        Write-Host "  deploy  - Deploy the entire system (default)" -ForegroundColor White
        Write-Host "  stop    - Stop all services" -ForegroundColor White
        Write-Host "  restart - Restart all services" -ForegroundColor White
        Write-Host "  logs    - View logs (optional service name)" -ForegroundColor White
        Write-Host "  status  - Show service status" -ForegroundColor White
        Write-Host "  help    - Show this help message" -ForegroundColor White
    }
    default {
        Write-Log "Unknown command: $Command" "ERROR"
        Write-Host "Use '.\deploy.ps1 help' for usage information" -ForegroundColor Red
        exit 1
    }
}