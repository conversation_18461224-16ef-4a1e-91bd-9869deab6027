# requirements/base.txt
# Core Dependencies for LangGraph AI Agent

# ========== LANGGRAPH AND AI FRAMEWORKS ==========
langgraph>=0.2.0
langchain>=0.3.0
langchain-core>=0.3.0
langchain-community>=0.3.0

# ========== PERPLEXITY AND AI APIS ==========
openai>=1.35.0
httpx>=0.27.0
requests>=2.31.0

# ========== WEB FRAMEWORK ==========
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# ========== DATABASE ==========
asyncpg>=0.29.0
psycopg2-binary>=2.9.9
sqlalchemy>=2.0.23
alembic>=1.13.0

# ========== VECTOR DATABASE ==========
pgvector>=0.2.4
numpy>=1.24.0
sentence-transformers>=2.2.2

# ========== CACHING ==========
redis>=5.0.0
hiredis>=2.3.2

# ========== MESSAGE QUEUE ==========
celery>=5.3.4
kombu>=5.3.4

# ========== MONITORING AND LOGGING ==========
prometheus-client>=0.19.0
structlog>=23.2.0
rich>=13.7.0

# ========== UTILITIES ==========
pydantic-core>=2.14.0
typing-extensions>=4.8.0
python-dotenv>=1.0.0
click>=8.1.7
python-multipart>=0.0.6

# ========== VALIDATION ==========
jsonschema>=4.20.0
marshmallow>=3.20.0

# ========== ASYNC ==========
asyncio-throttle>=1.0.2
aiofiles>=23.2.1
aioredis>=2.0.1

# ========== SECURITY ==========
cryptography>=41.0.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
bcrypt>=4.1.2

# ========== HTTP CLIENT ==========
aiohttp>=3.9.0
httpx>=0.25.0