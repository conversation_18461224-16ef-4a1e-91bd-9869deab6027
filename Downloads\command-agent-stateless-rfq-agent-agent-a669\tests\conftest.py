"""
Comprehensive Test Suite for LangGraph AI Agent System
Tests all 12-Factor Agent implementations with full coverage
"""

import pytest
import asyncio
import os
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path
import tempfile
import logging
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Test configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_database():
    """Set up test database"""
    # Use SQLite for testing
    test_db_path = tempfile.mktemp(suffix=".db")
    
    # Initialize test database
    from services.agent.database.connection import init_test_database
    db = await init_test_database(f"sqlite:///{test_db_path}")
    
    yield db
    
    # Cleanup
    await db.disconnect()
    if os.path.exists(test_db_path):
        os.unlink(test_db_path)

@pytest.fixture(scope="session")
async def test_redis():
    """Set up test Redis connection"""
    import fakeredis.aioredis
    redis = fakeredis.aioredis.FakeRedis()
    yield redis
    await redis.close()

@pytest.fixture
async def test_services(test_database, test_redis):
    """Initialize test services"""
    from services.agent.services.memory_service import MemoryService
    from services.agent.services.perplexity_service import PerplexityService
    from services.agent.services.error_manager import ErrorManager
    from services.agent.services.stateless_reducer import StatelessAgentService
    from services.agent.services.universal_input_handler import UniversalInputHandler
    
    # Create test instances with mocked dependencies
    services = {
        'memory': MemoryService(),
        'perplexity': PerplexityService(),  # Will be mocked
        'error_manager': ErrorManager(),
        'stateless': StatelessAgentService(),
        'input_handler': UniversalInputHandler()
    }
    
    yield services

@pytest.fixture
async def test_agents(test_services):
    """Initialize test agents"""
    from services.agent.agents.market_research_agent import MarketResearchAgent
    from services.agent.agents.vendor_discovery_agent import VendorDiscoveryAgent
    from services.agent.agents.document_generation_agent import DocumentGenerationAgent
    from services.agent.agents.multi_agent_orchestrator import MultiAgentOrchestrator
    
    agents = {
        'market_research': MarketResearchAgent(),
        'vendor_discovery': VendorDiscoveryAgent(),
        'document_generation': DocumentGenerationAgent(),
        'orchestrator': MultiAgentOrchestrator()
    }
    
    yield agents

@pytest.fixture
def sample_rfq_request():
    """Sample RFQ request for testing"""
    return {
        "rfq_request": "Need 50 business laptops for our Mumbai office. Budget around $50,000. Urgent requirement.",
        "region": "India",
        "category": "IT",
        "budget_range": "$50,000",
        "urgency": "urgent",
        "department": "procurement"
    }

@pytest.fixture
def mock_perplexity_response():
    """Mock Perplexity API response"""
    return {
        "content": "Market research shows laptop prices ranging from $800-1200 for business grade. Top suppliers include Dell, HP, Lenovo.",
        "sources": ["market-report-1", "supplier-database"],
        "confidence": 0.85
    }

class TestConfig:
    """Test configuration class"""
    
    # Test database settings
    TEST_DATABASE_URL = "sqlite:///test.db"
    TEST_REDIS_URL = "redis://localhost:6379/15"
    
    # Test API keys (mock)
    TEST_PERPLEXITY_API_KEY = "test-key-12345"
    
    # Test timeouts
    DEFAULT_TIMEOUT = 30
    LONG_TIMEOUT = 120
    
    # Test data paths
    TEST_DATA_DIR = Path(__file__).parent / "data"
    TEST_FIXTURES_DIR = Path(__file__).parent / "fixtures"
    
    @classmethod
    def setup_test_environment(cls):
        """Set up test environment variables"""
        os.environ.update({
            "DATABASE_URL": cls.TEST_DATABASE_URL,
            "REDIS_URL": cls.TEST_REDIS_URL,
            "PERPLEXITY_API_KEY": cls.TEST_PERPLEXITY_API_KEY,
            "ENVIRONMENT": "testing",
            "LOG_LEVEL": "DEBUG"
        })

class TestBase:
    """Base test class with common utilities"""
    
    @staticmethod
    def assert_successful_response(response: Dict[str, Any]):
        """Assert response indicates success"""
        assert response is not None
        assert response.get("success") is True
        assert "error" not in response or response["error"] is None
    
    @staticmethod
    def assert_error_response(response: Dict[str, Any], expected_error: str = None):
        """Assert response indicates error"""
        assert response is not None
        assert response.get("success") is False
        if expected_error:
            assert expected_error in str(response.get("error", ""))
    
    @staticmethod
    def assert_has_required_fields(data: Dict[str, Any], required_fields: List[str]):
        """Assert data has all required fields"""
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"
            assert data[field] is not None, f"Field {field} is None"
    
    @staticmethod
    async def wait_for_condition(condition_func, timeout: int = 10, interval: float = 0.1):
        """Wait for a condition to become true"""
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            if await condition_func():
                return True
            await asyncio.sleep(interval)
        return False

class MockPerplexityService:
    """Mock Perplexity service for testing"""
    
    def __init__(self):
        self.call_count = 0
        self.responses = {}
    
    async def research_with_sources(self, query: str, context: str = None) -> Dict[str, Any]:
        """Mock research with sources"""
        self.call_count += 1
        
        # Return different responses based on query
        if "market" in query.lower():
            return {
                "content": "Market analysis shows strong demand for the requested items.",
                "sources": ["market-report-1", "industry-analysis-2"],
                "confidence": 0.9
            }
        elif "vendor" in query.lower():
            return {
                "content": "Found multiple qualified vendors in the specified region.",
                "sources": ["vendor-database", "supplier-directory"],
                "confidence": 0.85
            }
        else:
            return {
                "content": "Research completed successfully.",
                "sources": ["general-database"],
                "confidence": 0.8
            }
    
    async def generate_response(self, prompt: str, context: str = None) -> str:
        """Mock response generation"""
        self.call_count += 1
        return f"Generated response for: {prompt[:50]}..."
    
    def reset(self):
        """Reset mock state"""
        self.call_count = 0
        self.responses.clear()

# Global test configuration
pytest_plugins = ["pytest_asyncio"]

def pytest_configure(config):
    """Configure pytest"""
    TestConfig.setup_test_environment()
    
    # Add custom markers
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "factor1: test Factor 1 implementation"
    )
    config.addinivalue_line(
        "markers", "factor2: test Factor 2 implementation"
    )
    config.addinivalue_line(
        "markers", "factor3: test Factor 3 implementation"
    )
    config.addinivalue_line(
        "markers", "factor4: test Factor 4 implementation"
    )
    config.addinivalue_line(
        "markers", "factor5: test Factor 5 implementation"
    )
    config.addinivalue_line(
        "markers", "factor6: test Factor 6 implementation"
    )
    config.addinivalue_line(
        "markers", "factor7: test Factor 7 implementation"
    )
    config.addinivalue_line(
        "markers", "factor8: test Factor 8 implementation"
    )
    config.addinivalue_line(
        "markers", "factor9: test Factor 9 implementation"
    )
    config.addinivalue_line(
        "markers", "factor10: test Factor 10 implementation"
    )
    config.addinivalue_line(
        "markers", "factor11: test Factor 11 implementation"
    )
    config.addinivalue_line(
        "markers", "factor12: test Factor 12 implementation"
    )

def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    for item in items:
        # Add slow marker to tests that might be slow
        if "agent" in item.nodeid or "workflow" in item.nodeid:
            item.add_marker(pytest.mark.slow)
        
        # Auto-add integration marker for certain test files
        if "integration" in item.fspath.basename:
            item.add_marker(pytest.mark.integration)
        elif "unit" in item.fspath.basename:
            item.add_marker(pytest.mark.unit)
        elif "e2e" in item.fspath.basename:
            item.add_marker(pytest.mark.e2e)

@pytest.fixture
def mock_perplexity():
    """Provide mock Perplexity service"""
    return MockPerplexityService()