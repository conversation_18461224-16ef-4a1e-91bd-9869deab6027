"""
Embedding Service for generating semantic embeddings
Simple implementation that can be extended with more sophisticated models
"""

import structlog
import hashlib
import json
from typing import List, Dict, Any
import numpy as np
from datetime import datetime

logger = structlog.get_logger(__name__)

class EmbeddingService:
    """Service for generating embeddings for semantic search"""
    
    def __init__(self, dimension: int = 1536):
        self.dimension = dimension
    
    async def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for text
        This is a simple implementation - in production, you would use
        a proper embedding model like OpenAI's text-embedding-ada-002
        or sentence-transformers
        """
        logger.debug("Generating embedding", text_length=len(text))
        
        try:
            # Simple deterministic embedding based on text content
            # In production, replace with actual embedding model
            
            # Create a hash-based seed for reproducibility
            text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
            seed = int(text_hash[:8], 16)
            np.random.seed(seed)
            
            # Generate embedding based on text characteristics
            embedding = np.random.normal(0, 0.1, self.dimension)
            
            # Add some semantic meaning based on keywords
            keywords = {
                'rfq': 0.1, 'request': 0.08, 'procurement': 0.12, 'vendor': 0.09,
                'quote': 0.07, 'supplier': 0.1, 'purchase': 0.08, 'budget': 0.06,
                'urgent': 0.05, 'critical': 0.06, 'laptop': 0.04, 'desktop': 0.04,
                'furniture': 0.04, 'it': 0.05, 'technology': 0.05, 'market': 0.07,
                'price': 0.06, 'cost': 0.05, 'delivery': 0.04, 'quality': 0.05
            }
            
            text_lower = text.lower()
            for keyword, weight in keywords.items():
                if keyword in text_lower:
                    # Modify embedding based on keyword presence
                    keyword_seed = hash(keyword) % 1000
                    np.random.seed(keyword_seed)
                    keyword_vector = np.random.normal(0, weight, self.dimension)
                    embedding += keyword_vector
            
            # Normalize the embedding
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = embedding / norm
            
            # Convert to list for JSON serialization
            return embedding.tolist()
            
        except Exception as e:
            logger.error("Failed to generate embedding", error=str(e), text_length=len(text))
            # Return zero vector as fallback
            return [0.0] * self.dimension
    
    async def generate_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        logger.info("Generating batch embeddings", batch_size=len(texts))
        
        embeddings = []
        for text in texts:
            embedding = await self.generate_embedding(text)
            embeddings.append(embedding)
        
        return embeddings
    
    def cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """Calculate cosine similarity between two embeddings"""
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error("Failed to calculate cosine similarity", error=str(e))
            return 0.0
    
    async def find_similar_embeddings(
        self, 
        query_embedding: List[float], 
        candidate_embeddings: List[Dict[str, Any]],
        threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Find similar embeddings from a list of candidates"""
        logger.debug("Finding similar embeddings", 
                    candidates_count=len(candidate_embeddings), 
                    threshold=threshold)
        
        similar_items = []
        
        for item in candidate_embeddings:
            candidate_embedding = item.get('embedding', [])
            if not candidate_embedding:
                continue
            
            similarity = self.cosine_similarity(query_embedding, candidate_embedding)
            
            if similarity >= threshold:
                similar_items.append({
                    **item,
                    'similarity': similarity
                })
        
        # Sort by similarity (highest first)
        similar_items.sort(key=lambda x: x['similarity'], reverse=True)
        
        return similar_items