-- database/init/01_init_extensions.sql
-- Initialize PostgreSQL with pgvector for AI agent memory system

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- <PERSON><PERSON> schemas
CREATE SCHEMA IF NOT EXISTS agent_core;
CREATE SCHEMA IF NOT EXISTS agent_memory;
CREATE SCHEMA IF NOT EXISTS agent_workflows;
CREATE SCHEMA IF NOT EXISTS agent_monitoring;

-- ========== CORE AGENT TABLES ==========

-- Agent definitions and metadata
CREATE TABLE agent_core.agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    capabilities JSONB NOT NULL DEFAULT '[]'::jsonb,
    configuration JSONB NOT NULL DEFAULT '{}'::jsonb,
    version VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deprecated')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tool registry for 12-Factor Agent principle
CREATE TABLE agent_core.tools (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    schema JSONB NOT NULL,
    implementation TEXT NOT NULL,
    category VARCHAR(100),
    version VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Prompt management system (Factor 2: Own Your Prompts)
CREATE TABLE agent_core.prompts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    variables JSONB DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    performance_metrics JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(name, version)
);

-- ========== WORKFLOW MANAGEMENT ==========

-- Workflow definitions and state management
CREATE TABLE agent_workflows.workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    agent_id UUID REFERENCES agent_core.agents(id),
    state_schema JSONB NOT NULL,
    configuration JSONB NOT NULL DEFAULT '{}'::jsonb,
    version VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow executions (Factor 5: Unified State Management)
CREATE TABLE agent_workflows.executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES agent_workflows.workflows(id),
    user_id VARCHAR(255),
    input_data JSONB NOT NULL,
    current_state JSONB NOT NULL DEFAULT '{}'::jsonb,
    execution_state VARCHAR(50) NOT NULL DEFAULT 'pending' 
        CHECK (execution_state IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    error_info JSONB,
    metadata JSONB DEFAULT '{}'::jsonb,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Checkpoint system for Factor 6: Launch/Pause/Resume
CREATE TABLE agent_workflows.checkpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_id UUID REFERENCES agent_workflows.executions(id),
    step_name VARCHAR(255) NOT NULL,
    state_snapshot JSONB NOT NULL,
    context_data JSONB DEFAULT '{}'::jsonb,
    checkpoint_type VARCHAR(50) NOT NULL DEFAULT 'automatic' 
        CHECK (checkpoint_type IN ('automatic', 'manual', 'error', 'pause')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Human-in-the-loop system (Factor 7: Contact Humans)
CREATE TABLE agent_workflows.human_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_id UUID REFERENCES agent_workflows.executions(id),
    task_type VARCHAR(100) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    context_data JSONB NOT NULL,
    assigned_to VARCHAR(255),
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' 
        CHECK (status IN ('pending', 'in_progress', 'completed', 'rejected', 'escalated')),
    response_data JSONB,
    deadline TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- ========== MEMORY SYSTEM (Factor 3: Context Window Management) ==========

-- Semantic memory with pgvector embeddings
CREATE TABLE agent_memory.memories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI/Perplexity embedding dimension
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
    memory_type VARCHAR(100) NOT NULL,
    source VARCHAR(255),
    importance_score FLOAT DEFAULT 0.5 CHECK (importance_score >= 0 AND importance_score <= 1),
    access_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Memory clusters for efficient retrieval
CREATE TABLE agent_memory.memory_clusters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    centroid vector(1536),
    memory_ids UUID[] NOT NULL,
    cluster_metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Context window management
CREATE TABLE agent_memory.context_windows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_id UUID REFERENCES agent_workflows.executions(id),
    window_data JSONB NOT NULL,
    memory_references UUID[] DEFAULT ARRAY[]::UUID[],
    token_count INTEGER NOT NULL,
    priority_score FLOAT DEFAULT 0.5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversation threads and state
CREATE TABLE agent_memory.conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    conversation_data JSONB NOT NULL,
    summary TEXT,
    embedding vector(1536),
    message_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========== ERROR AND MONITORING ==========

-- Error patterns and resolution (Factor 9: Compact Errors)
CREATE TABLE agent_monitoring.error_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    error_signature VARCHAR(500) NOT NULL,
    error_type VARCHAR(100) NOT NULL,
    description TEXT,
    solution_steps JSONB,
    embedding vector(1536),
    occurrence_count INTEGER DEFAULT 1,
    resolved_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Execution logs and metrics
CREATE TABLE agent_monitoring.execution_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_id UUID REFERENCES agent_workflows.executions(id),
    level VARCHAR(20) NOT NULL CHECK (level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')),
    message TEXT NOT NULL,
    context_data JSONB DEFAULT '{}'::jsonb,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance metrics
CREATE TABLE agent_monitoring.performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(255) NOT NULL,
    metric_value FLOAT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========== RFQ-SPECIFIC TABLES ==========

-- RFQ workflow state
CREATE TABLE agent_workflows.rfq_states (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_id UUID REFERENCES agent_workflows.executions(id),
    rfq_id VARCHAR(255) NOT NULL UNIQUE,
    current_stage VARCHAR(100) NOT NULL,
    workflow_step VARCHAR(255) NOT NULL,
    progress_percentage INTEGER DEFAULT 0,
    vendors JSONB DEFAULT '[]'::jsonb,
    quotes_received JSONB DEFAULT '[]'::jsonb,
    market_intelligence JSONB DEFAULT '{}'::jsonb,
    human_approval_pending BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vendor information
CREATE TABLE agent_workflows.vendors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    rating FLOAT CHECK (rating >= 0 AND rating <= 5),
    specialties JSONB DEFAULT '[]'::jsonb,
    location VARCHAR(255),
    contact_info JSONB DEFAULT '{}'::jsonb,
    performance_history JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========== INDEXES FOR PERFORMANCE ==========

-- Vector similarity indexes
CREATE INDEX ON agent_memory.memories USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX ON agent_memory.memory_clusters USING ivfflat (centroid vector_cosine_ops) WITH (lists = 50);
CREATE INDEX ON agent_memory.conversations USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX ON agent_monitoring.error_patterns USING ivfflat (embedding vector_cosine_ops) WITH (lists = 50);

-- General performance indexes
CREATE INDEX idx_executions_workflow_id ON agent_workflows.executions(workflow_id);
CREATE INDEX idx_executions_user_id ON agent_workflows.executions(user_id);
CREATE INDEX idx_executions_state ON agent_workflows.executions(execution_state);
CREATE INDEX idx_checkpoints_execution_id ON agent_workflows.checkpoints(execution_id);
CREATE INDEX idx_human_tasks_status ON agent_workflows.human_tasks(status);
CREATE INDEX idx_memory_type ON agent_memory.memories(memory_type);
CREATE INDEX idx_memory_importance ON agent_memory.memories(importance_score DESC);
CREATE INDEX idx_error_patterns_type ON agent_monitoring.error_patterns(error_type);
CREATE INDEX idx_execution_logs_level ON agent_monitoring.execution_logs(level);
CREATE INDEX idx_performance_metrics_name ON agent_monitoring.performance_metrics(metric_name);

-- Text search indexes
CREATE INDEX idx_memories_content_gin ON agent_memory.memories USING gin(to_tsvector('english', content));
CREATE INDEX idx_prompts_content_gin ON agent_core.prompts USING gin(to_tsvector('english', content));

-- ========== FUNCTIONS AND TRIGGERS ==========

-- Update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agent_core.agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tools_updated_at BEFORE UPDATE ON agent_core.tools FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON agent_workflows.workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_executions_updated_at BEFORE UPDATE ON agent_workflows.executions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_memory_clusters_updated_at BEFORE UPDATE ON agent_memory.memory_clusters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON agent_memory.conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_error_patterns_updated_at BEFORE UPDATE ON agent_monitoring.error_patterns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rfq_states_updated_at BEFORE UPDATE ON agent_workflows.rfq_states FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON agent_workflows.vendors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Memory access tracking function
CREATE OR REPLACE FUNCTION track_memory_access()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE agent_memory.memories 
    SET access_count = access_count + 1, last_accessed = NOW()
    WHERE id = NEW.memory_id;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA agent_core TO agent_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA agent_memory TO agent_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA agent_workflows TO agent_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA agent_monitoring TO agent_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA agent_core TO agent_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA agent_memory TO agent_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA agent_workflows TO agent_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA agent_monitoring TO agent_user;