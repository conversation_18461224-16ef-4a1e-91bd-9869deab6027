"""
Comprehensive Test Suite for 12-Factor Agent Implementation
Tests Factors 7-12 with real scenarios and edge cases
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from .conftest import TestBase, MockPerplexityService


class TestFactors7To12(TestBase):
    """Tests for Factors 7-12 of the 12-Factor Agent"""
    
    # ========== FACTOR 7: CONTACT HUMANS WITH TOOL CALLS ==========
    
    @pytest.mark.factor7
    @pytest.mark.unit
    async def test_factor7_human_task_creation(self, test_services, test_database):
        """Test Factor 7: Contact Humans with Tool Calls"""
        from services.agent.services.human_approval_service import HumanApprovalService
        
        approval_service = HumanApprovalService()
        
        # Test human task creation
        task_data = {
            "execution_id": "test_execution_123",
            "task_type": "vendor_approval",
            "title": "Approve Vendor Selection",
            "description": "Please review and approve the selected vendors for RFQ-001",
            "context_data": {
                "rfq_id": "RFQ-001",
                "vendors": ["Vendor A", "Vendor B", "Vendor C"],
                "budget_impact": "$45,000"
            },
            "priority": "high",
            "deadline": (datetime.utcnow() + timedelta(hours=24)).isoformat()
        }
        
        task_id = await approval_service.create_human_task(task_data, test_database)
        assert task_id is not None
        assert len(task_id) > 0
    
    @pytest.mark.factor7
    @pytest.mark.integration
    async def test_factor7_human_workflow_integration(self, test_services, test_database):
        """Test human-in-the-loop workflow integration"""
        from services.agent.services.human_approval_service import HumanApprovalService
        
        approval_service = HumanApprovalService()
        execution_id = "test_human_workflow_123"
        
        # Create human task
        task_data = {
            "execution_id": execution_id,
            "task_type": "budget_approval",
            "title": "Budget Approval Required",
            "description": "Budget exceeds threshold, approval needed",
            "context_data": {"requested_budget": "$75,000", "threshold": "$50,000"},
            "priority": "high"
        }
        
        task_id = await approval_service.create_human_task(task_data, test_database)
        
        # Test task retrieval
        pending_tasks = await approval_service.get_pending_tasks(test_database)
        assert len(pending_tasks) > 0
        assert any(task["id"] == task_id for task in pending_tasks)
        
        # Test task response
        response_data = {"approved": True, "comments": "Approved with conditions"}
        await approval_service.respond_to_task(task_id, response_data, test_database)
        
        # Verify task completion
        completed_task = await approval_service.get_task_by_id(task_id, test_database)
        assert completed_task["status"] == "completed"
        assert completed_task["response_data"]["approved"] is True
    
    # ========== FACTOR 8: OWN YOUR CONTROL FLOW ==========
    
    @pytest.mark.factor8
    @pytest.mark.unit
    async def test_factor8_langgraph_state_machine(self, test_agents):
        """Test Factor 8: Own Your Control Flow"""
        from services.agent.agents.rfq_agent import RFQWorkflowAgent
        
        # Test state machine structure
        rfq_agent = RFQWorkflowAgent()
        
        # Verify workflow has proper state machine structure
        workflow_capabilities = rfq_agent.get_workflow_capabilities()
        assert "workflow_steps" in workflow_capabilities
        assert "state_transitions" in workflow_capabilities
        assert len(workflow_capabilities["workflow_steps"]) > 0
    
    @pytest.mark.factor8
    @pytest.mark.integration
    async def test_factor8_conditional_flow_control(self, test_agents, sample_rfq_request):
        """Test conditional flow control in LangGraph"""
        orchestrator = test_agents['orchestrator']
        
        # Test workflow execution with conditional logic
        result = await orchestrator.process_rfq_request(
            rfq_request=sample_rfq_request["rfq_request"],
            region=sample_rfq_request["region"],
            category=sample_rfq_request["category"],
            urgency=sample_rfq_request["urgency"]
        )
        
        # Verify conditional execution
        assert result["success"] is True
        assert "workflow_id" in result
        assert result["agents_executed"] is not None
        assert len(result["agents_executed"]) > 0
    
    # ========== FACTOR 9: COMPACT ERRORS INTO CONTEXT WINDOW ==========
    
    @pytest.mark.factor9
    @pytest.mark.unit
    async def test_factor9_error_compaction(self, test_services):
        """Test Factor 9: Compact Errors into Context Window"""
        error_manager = test_services['error_manager']
        
        # Test error compaction
        test_error = Exception("Test error with very long message that would normally consume too much context window space and needs to be compacted for efficient processing")
        
        context = {
            "execution_id": "test_123",
            "step": "market_research",
            "input_data": {"query": "test query"}
        }
        
        compacted_error = await error_manager.compact_error_for_context(
            error=test_error,
            context=context,
            execution_id="test_123",
            step_name="market_research"
        )
        
        # Verify error is compacted
        assert len(compacted_error) < len(str(test_error))
        assert "market_research" in compacted_error
        assert "test_123" in compacted_error
    
    @pytest.mark.factor9
    @pytest.mark.integration
    async def test_factor9_error_pattern_recognition(self, test_services):
        """Test error pattern recognition and learning"""
        error_manager = test_services['error_manager']
        
        # Simulate multiple similar errors
        similar_errors = [
            Exception("Network timeout during API call to vendor service"),
            Exception("Network timeout during API call to market service"),
            Exception("Network timeout during API call to document service")
        ]
        
        patterns_found = []
        for i, error in enumerate(similar_errors):
            compacted = await error_manager.compact_error_for_context(
                error=error,
                context={"execution_id": f"test_{i}"},
                execution_id=f"test_{i}",
                step_name="api_call"
            )
            patterns_found.append(compacted)
        
        # Should recognize the pattern
        assert len(patterns_found) == 3
        assert all("timeout" in pattern for pattern in patterns_found)
    
    # ========== FACTOR 10: SMALL, FOCUSED AGENTS ==========
    
    @pytest.mark.factor10
    @pytest.mark.unit
    async def test_factor10_specialized_agent_capabilities(self, test_agents):
        """Test Factor 10: Small, Focused Agents"""
        
        # Test market research agent
        market_agent = test_agents['market_research']
        capabilities = market_agent.get_agent_capabilities()
        
        assert capabilities["agent_type"] == "market_research"
        assert "market_intelligence" in capabilities["capabilities"]
        assert len(capabilities["capabilities"]) > 0
        
        # Test vendor discovery agent  
        vendor_agent = test_agents['vendor_discovery']
        capabilities = vendor_agent.get_agent_capabilities()
        
        assert capabilities["agent_type"] == "vendor_discovery"
        assert "vendor_identification" in capabilities["capabilities"]
        
        # Test document generation agent
        doc_agent = test_agents['document_generation']
        capabilities = doc_agent.get_agent_capabilities()
        
        assert capabilities["agent_type"] == "document_generation"
        assert "rfq_generation" in capabilities["capabilities"]
    
    @pytest.mark.factor10
    @pytest.mark.integration
    async def test_factor10_multi_agent_coordination(self, test_agents, sample_rfq_request):
        """Test multi-agent coordination"""
        orchestrator = test_agents['orchestrator']
        
        # Test coordinated execution
        result = await orchestrator.process_rfq_request(
            rfq_request=sample_rfq_request["rfq_request"],
            region=sample_rfq_request["region"],
            category=sample_rfq_request["category"]
        )
        
        # Verify multiple agents were coordinated
        assert result["success"] is True
        assert "market_research" in result
        assert "vendor_discovery" in result
        assert "document_generation" in result
        
        # Verify each agent contributed
        if result["market_research"]:
            assert result["market_research"].get("success") is not False
        if result["vendor_discovery"]:
            assert result["vendor_discovery"].get("success") is not False
    
    # ========== FACTOR 11: TRIGGER FROM ANYWHERE ==========
    
    @pytest.mark.factor11
    @pytest.mark.unit
    async def test_factor11_universal_input_handling(self, test_services):
        """Test Factor 11: Trigger from Anywhere"""
        from services.agent.services.universal_input_handler import UniversalInputHandler, InputContext, InputSource, InputFormat
        
        input_handler = test_services['input_handler']
        
        # Test different input formats
        test_inputs = [
            {
                "input": "Need 20 laptops for Mumbai office",
                "context": InputContext(
                    source=InputSource.API,
                    format=InputFormat.TEXT,
                    timestamp=datetime.utcnow()
                )
            },
            {
                "input": {"message": "Procurement request for office furniture", "budget": "$25000"},
                "context": InputContext(
                    source=InputSource.CHAT,
                    format=InputFormat.JSON,
                    timestamp=datetime.utcnow()
                )
            }
        ]
        
        for test_case in test_inputs:
            result = await input_handler.process_input(
                test_case["input"],
                test_case["context"]
            )
            
            assert result["success"] is True
            assert result["input_processed"] is True
            assert "intent" in result
    
    @pytest.mark.factor11
    @pytest.mark.integration
    async def test_factor11_multi_source_routing(self, test_services, sample_rfq_request):
        """Test routing from multiple input sources"""
        from services.agent.services.universal_input_handler import UniversalInputHandler, InputContext, InputSource, InputFormat
        
        input_handler = test_services['input_handler']
        
        # Test email input
        email_context = InputContext(
            source=InputSource.EMAIL,
            format=InputFormat.EMAIL_MSG,
            timestamp=datetime.utcnow(),
            sender_info={"email": "<EMAIL>"}
        )
        
        email_data = {
            "subject": "RFQ Request - Laptops",
            "body": sample_rfq_request["rfq_request"],
            "sender": "<EMAIL>"
        }
        
        result = await input_handler.process_input(email_data, email_context)
        assert result["success"] is True
        assert result["source"] == "email"
        assert "workflow_response" in result
    
    # ========== FACTOR 12: MAKE YOUR AGENT A STATELESS REDUCER ==========
    
    @pytest.mark.factor12
    @pytest.mark.unit
    async def test_factor12_stateless_operations(self, test_services):
        """Test Factor 12: Make Your Agent a Stateless Reducer"""
        from services.agent.services.stateless_reducer import StatelessAgentService, AgentState, ActionType
        
        stateless_service = test_services['stateless']
        
        # Test immutable state creation
        initial_state = stateless_service.create_initial_state(
            execution_id="test_123",
            context={"rfq_type": "laptop_procurement"}
        )
        
        assert initial_state.execution_id == "test_123"
        assert initial_state.status == "initialized"
        assert initial_state.context["rfq_type"] == "laptop_procurement"
        
        # Test pure function state update
        action = stateless_service.create_action(
            ActionType.UPDATE_PROGRESS,
            {"progress": 0.5}
        )
        
        new_state = stateless_service.apply_action(initial_state, action)
        
        # Verify immutability - original state unchanged
        assert initial_state.progress == 0.0
        assert new_state.progress == 0.5
        assert initial_state.execution_id == new_state.execution_id
    
    @pytest.mark.factor12
    @pytest.mark.integration
    async def test_factor12_functional_workflow_composition(self, test_services):
        """Test functional workflow composition"""
        from services.agent.services.functional_workflow_service import FunctionalWorkflowService
        
        functional_service = FunctionalWorkflowService()
        
        # Test functional workflow execution
        result = await functional_service.execute_rfq_workflow(
            rfq_request="Need 10 desks for office",
            user_id="test_user"
        )
        
        assert result["success"] is True
        assert "execution_id" in result
        assert "final_status" in result
        assert "functional_guarantees" in result["service_info"]
        
        # Verify functional guarantees
        guarantees = result["service_info"]["functional_guarantees"]
        assert "Immutable state management" in guarantees
        assert "Pure function operations" in guarantees
    
    @pytest.mark.factor12
    @pytest.mark.integration
    async def test_factor12_parallel_functional_execution(self, test_services):
        """Test parallel functional execution"""
        from services.agent.services.functional_workflow_service import FunctionalWorkflowService
        
        functional_service = FunctionalWorkflowService()
        
        # Test parallel workflow branches
        result = await functional_service.execute_parallel_workflow_branches(
            rfq_request="Procurement request for office supplies"
        )
        
        assert result["success"] is True
        assert "parallel_results" in result
        assert "research" in result["parallel_results"]
        assert "vendor" in result["parallel_results"]
        assert "document" in result["parallel_results"]
        assert result["pattern"] == "parallel_functional_execution"
    
    # ========== INTEGRATION TESTS ACROSS FACTORS ==========
    
    @pytest.mark.integration
    @pytest.mark.slow
    async def test_complete_12_factor_workflow(self, test_services, test_agents, sample_rfq_request):
        """Test complete workflow touching all 12 factors"""
        from services.agent.services.universal_input_handler import InputContext, InputSource, InputFormat
        
        # Factor 11: Universal input
        input_handler = test_services['input_handler']
        input_context = InputContext(
            source=InputSource.API,
            format=InputFormat.JSON,
            timestamp=datetime.utcnow()
        )
        
        # Process input through all factors
        input_result = await input_handler.process_input(
            sample_rfq_request,
            input_context
        )
        
        assert input_result["success"] is True
        
        # Factor 10: Multi-agent orchestration
        orchestrator = test_agents['orchestrator']
        workflow_result = await orchestrator.process_rfq_request(
            rfq_request=sample_rfq_request["rfq_request"],
            region=sample_rfq_request["region"],
            category=sample_rfq_request["category"],
            budget_range=sample_rfq_request["budget_range"],
            urgency=sample_rfq_request["urgency"]
        )
        
        # Verify end-to-end execution
        assert workflow_result["success"] is True
        assert "execution_id" in workflow_result
        assert len(workflow_result["agents_executed"]) > 0
        
        # Verify factors are represented
        self.assert_has_required_fields(workflow_result, [
            "execution_id",  # Factor 5: State management
            "agents_executed",  # Factor 10: Multi-agent
            "execution_time",  # Factor 8: Control flow
            "overall_quality_score"  # Factor 9: Error management
        ])
    
    @pytest.mark.e2e
    @pytest.mark.slow  
    async def test_end_to_end_rfq_scenario(self, test_services, test_agents, test_database):
        """End-to-end RFQ scenario test"""
        
        # Complete RFQ workflow scenario
        rfq_data = {
            "rfq_request": "We need 25 high-performance laptops for our development team in Bangalore. Budget is flexible up to $75,000. This is urgent as team starts next week.",
            "region": "India",
            "category": "IT",
            "budget_range": "$75,000",
            "urgency": "urgent",
            "department": "engineering"
        }
        
        # Execute complete workflow
        orchestrator = test_agents['orchestrator']
        result = await orchestrator.process_rfq_request(**rfq_data)
        
        # Comprehensive validation
        assert result["success"] is True
        assert result["category"] == "IT"
        assert result["region"] == "India"
        
        # Validate market research results
        if "market_research" in result and result["market_research"]:
            market_result = result["market_research"]
            if market_result.get("success"):
                assert "price_analysis" in market_result or "research_quality_score" in market_result
        
        # Validate vendor discovery results  
        if "vendor_discovery" in result and result["vendor_discovery"]:
            vendor_result = result["vendor_discovery"]
            if vendor_result.get("success"):
                assert "total_vendors" in vendor_result
                assert vendor_result["total_vendors"] >= 0
        
        # Validate overall execution
        assert "overall_quality_score" in result
        assert result["overall_quality_score"] >= 0
        assert "execution_time" in result
        assert result["execution_time"] > 0