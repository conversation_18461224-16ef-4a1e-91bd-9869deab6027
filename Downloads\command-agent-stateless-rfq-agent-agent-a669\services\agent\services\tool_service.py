"""
Tool Service for managing structured tool registry
Implements Factor 4: Tools Are Just Structured Outputs
"""

import structlog
import json
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import importlib
import inspect

from ..core.database import db_service
from ..core.redis_client import cache_manager
from ..schemas.rfq import ToolDefinition, ToolCall

logger = structlog.get_logger(__name__)

class ToolService:
    """Service for managing tool registry and execution"""
    
    def __init__(self):
        self.registered_tools: Dict[str, Callable] = {}
        self.tool_cache_ttl = 1800  # 30 minutes
    
    async def register_tool(
        self,
        name: str,
        description: str,
        schema: Dict[str, Any],
        implementation: Callable,
        category: str = "general",
        version: str = "1.0.0"
    ) -> str:
        """
        Register a new tool in the registry
        Implements structured tool management for Factor 4
        """
        logger.info("Registering tool", name=name, category=category, version=version)
        
        try:
            # Validate tool implementation
            if not callable(implementation):
                raise ValueError("Tool implementation must be callable")
            
            # Get function signature for validation
            sig = inspect.signature(implementation)
            
            # Store tool definition in database
            tool_data = {
                "name": name,
                "description": description,
                "schema": schema,
                "implementation": f"{implementation.__module__}.{implementation.__name__}",
                "category": category,
                "version": version,
                "is_active": True
            }
            
            query = """
            INSERT INTO agent_core.tools (name, description, schema, implementation, category, version, is_active)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (name) DO UPDATE SET
                description = EXCLUDED.description,
                schema = EXCLUDED.schema,
                implementation = EXCLUDED.implementation,
                category = EXCLUDED.category,
                version = EXCLUDED.version,
                updated_at = NOW()
            RETURNING id
            """
            
            tool_id = await db_service.execute_single_query(query, tool_data)
            
            # Register in memory for fast access
            self.registered_tools[name] = implementation
            
            # Clear tool cache
            await cache_manager.invalidate_pattern("tools:*")
            
            logger.info("Tool registered successfully", 
                       name=name, 
                       tool_id=tool_id)
            
            return str(tool_id)
            
        except Exception as e:
            logger.error("Failed to register tool", 
                        name=name, 
                        error=str(e))
            raise
    
    async def get_tool(self, name: str) -> Optional[Callable]:
        """Get a tool implementation by name"""
        # Check in-memory cache first
        if name in self.registered_tools:
            return self.registered_tools[name]
        
        # Load from database
        tool_def = await self.get_tool_definition(name)
        if not tool_def:
            return None
        
        # Dynamically import the tool
        try:
            module_path, func_name = tool_def["implementation"].rsplit(".", 1)
            module = importlib.import_module(module_path)
            implementation = getattr(module, func_name)
            
            # Cache for future use
            self.registered_tools[name] = implementation
            return implementation
            
        except Exception as e:
            logger.error("Failed to load tool implementation", 
                        name=name, 
                        error=str(e))
            return None
    
    async def get_tool_definition(self, name: str) -> Optional[Dict[str, Any]]:
        """Get tool definition from database"""
        cache_key = f"tools:definition:{name}"
        
        # Check cache
        cached_def = await cache_manager.redis.get(cache_key)
        if cached_def:
            return cached_def
        
        # Query database
        query = """
        SELECT id, name, description, schema, implementation, category, version, is_active, created_at, updated_at
        FROM agent_core.tools
        WHERE name = $1 AND is_active = true
        """
        
        result = await db_service.execute_single_query(query, {"name": name})
        if not result:
            return None
        
        tool_def = dict(result)
        
        # Cache the result
        await cache_manager.redis.set(cache_key, tool_def, ex=self.tool_cache_ttl)
        
        return tool_def
    
    async def get_active_tools(self, category: str = None) -> List[Dict[str, Any]]:
        """Get all active tools, optionally filtered by category"""
        cache_key = f"tools:active:{category or 'all'}"
        
        # Check cache
        cached_tools = await cache_manager.redis.get(cache_key)
        if cached_tools:
            return cached_tools
        
        # Query database
        query = """
        SELECT id, name, description, schema, implementation, category, version, created_at, updated_at
        FROM agent_core.tools
        WHERE is_active = true
        """
        params = {}
        
        if category:
            query += " AND category = $1"
            params["category"] = category
        
        query += " ORDER BY category, name"
        
        results = await db_service.execute_query(query, params)
        
        tools = [dict(row) for row in results]
        
        # Cache the result
        await cache_manager.redis.set(cache_key, tools, ex=self.tool_cache_ttl)
        
        return tools
    
    async def execute_tool(
        self,
        name: str,
        input_data: Dict[str, Any],
        execution_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Execute a tool with structured input/output
        Implements Factor 4: Structured tool execution
        """
        start_time = datetime.utcnow()
        
        logger.info("Executing tool", name=name, input_keys=list(input_data.keys()))
        
        try:
            # Get tool implementation
            tool_func = await self.get_tool(name)
            if not tool_func:
                raise ValueError(f"Tool '{name}' not found or not active")
            
            # Get tool definition for validation
            tool_def = await self.get_tool_definition(name)
            if not tool_def:
                raise ValueError(f"Tool definition for '{name}' not found")
            
            # Validate input against schema
            await self._validate_tool_input(input_data, tool_def["schema"])
            
            # Execute the tool
            if asyncio.iscoroutinefunction(tool_func):
                output_data = await tool_func(**input_data)
            else:
                output_data = tool_func(**input_data)
            
            # Ensure output is structured
            if not isinstance(output_data, dict):
                output_data = {"result": output_data}
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create tool call record
            tool_call = ToolCall(
                tool_name=name,
                input_data=input_data,
                output_data=output_data,
                execution_time=execution_time,
                timestamp=start_time.isoformat(),
                success=True
            )
            
            # Log the tool call
            await self._log_tool_call(tool_call)
            
            logger.info("Tool executed successfully", 
                       name=name, 
                       execution_time=execution_time)
            
            return output_data
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create error tool call record
            tool_call = ToolCall(
                tool_name=name,
                input_data=input_data,
                output_data={},
                execution_time=execution_time,
                timestamp=start_time.isoformat(),
                success=False,
                error_info=str(e)
            )
            
            await self._log_tool_call(tool_call)
            
            logger.error("Tool execution failed", 
                        name=name, 
                        error=str(e),
                        execution_time=execution_time)
            
            raise
    
    async def _validate_tool_input(self, input_data: Dict[str, Any], schema: Dict[str, Any]):
        """Validate tool input against schema"""
        # Basic validation - can be enhanced with jsonschema
        required_fields = schema.get("required", [])
        properties = schema.get("properties", {})
        
        # Check required fields
        for field in required_fields:
            if field not in input_data:
                raise ValueError(f"Required field '{field}' missing from input")
        
        # Check field types (basic validation)
        for field, value in input_data.items():
            if field in properties:
                expected_type = properties[field].get("type")
                if expected_type and not self._validate_type(value, expected_type):
                    raise ValueError(f"Field '{field}' has invalid type. Expected {expected_type}")
    
    def _validate_type(self, value: Any, expected_type: str) -> bool:
        """Basic type validation"""
        type_mapping = {
            "string": str,
            "integer": int,
            "number": (int, float),
            "boolean": bool,
            "array": list,
            "object": dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True  # Unknown type, pass validation
    
    async def _log_tool_call(self, tool_call: ToolCall):
        """Log tool call for monitoring and debugging"""
        try:
            # Store in database for analytics
            query = """
            INSERT INTO agent_monitoring.tool_calls 
            (tool_name, input_data, output_data, execution_time, success, error_info, timestamp)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            """
            
            await db_service.execute_command(query, {
                "tool_name": tool_call.tool_name,
                "input_data": tool_call.input_data,
                "output_data": tool_call.output_data,
                "execution_time": tool_call.execution_time,
                "success": tool_call.success,
                "error_info": tool_call.error_info,
                "timestamp": tool_call.timestamp
            })
            
        except Exception as e:
            logger.error("Failed to log tool call", error=str(e))
    
    async def get_tool_statistics(self, name: str = None) -> Dict[str, Any]:
        """Get tool usage statistics"""
        try:
            if name:
                # Statistics for specific tool
                query = """
                SELECT 
                    COUNT(*) as total_calls,
                    COUNT(*) FILTER (WHERE success = true) as successful_calls,
                    COUNT(*) FILTER (WHERE success = false) as failed_calls,
                    AVG(execution_time) as avg_execution_time,
                    MAX(execution_time) as max_execution_time,
                    MIN(timestamp) as first_call,
                    MAX(timestamp) as last_call
                FROM agent_monitoring.tool_calls
                WHERE tool_name = $1
                """
                params = {"tool_name": name}
            else:
                # Overall statistics
                query = """
                SELECT 
                    tool_name,
                    COUNT(*) as total_calls,
                    COUNT(*) FILTER (WHERE success = true) as successful_calls,
                    COUNT(*) FILTER (WHERE success = false) as failed_calls,
                    AVG(execution_time) as avg_execution_time
                FROM agent_monitoring.tool_calls
                GROUP BY tool_name
                ORDER BY total_calls DESC
                """
                params = {}
            
            results = await db_service.execute_query(query, params)
            
            if name:
                # Single tool statistics
                if results:
                    stats = dict(results[0])
                    stats["success_rate"] = (
                        stats["successful_calls"] / stats["total_calls"] 
                        if stats["total_calls"] > 0 else 0
                    )
                    return stats
                else:
                    return {"tool_name": name, "total_calls": 0}
            else:
                # Multiple tools statistics
                stats = []
                for row in results:
                    tool_stats = dict(row)
                    tool_stats["success_rate"] = (
                        tool_stats["successful_calls"] / tool_stats["total_calls"]
                        if tool_stats["total_calls"] > 0 else 0
                    )
                    stats.append(tool_stats)
                return {"tools": stats}
            
        except Exception as e:
            logger.error("Failed to get tool statistics", error=str(e))
            return {"error": str(e)}
    
    async def deactivate_tool(self, name: str) -> bool:
        """Deactivate a tool"""
        try:
            query = """
            UPDATE agent_core.tools 
            SET is_active = false, updated_at = NOW()
            WHERE name = $1
            RETURNING id
            """
            
            result = await db_service.execute_single_query(query, {"name": name})
            if result:
                # Remove from memory cache
                self.registered_tools.pop(name, None)
                
                # Clear cache
                await cache_manager.invalidate_pattern(f"tools:*")
                
                logger.info("Tool deactivated", name=name)
                return True
            
            return False
            
        except Exception as e:
            logger.error("Failed to deactivate tool", name=name, error=str(e))
            return False