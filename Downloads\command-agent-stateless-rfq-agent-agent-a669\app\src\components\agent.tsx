"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import ReactMarkdown from 'react-markdown';
import { Send, FileText, Users, TrendingUp, AlertTriangle, CheckCircle, Clock, Building } from "lucide-react";

interface RFQState {
  rfq_id: string;
  current_stage: string;
  workflow_step: string;
  progress_percentage: number;
  vendors: VendorInfo[];
  quotes_received: any[];
  market_intelligence?: MarketIntelligence;
  human_approval_pending: boolean;
  created_at: string;
  updated_at: string;
}

interface VendorInfo {
  id: string;
  name: string;
  email: string;
  rating: number;
  specialties: string[];
  location: string;
}

interface MarketIntelligence {
  price_range: string;
  market_trends: string;
  supplier_recommendations: string[];
  risk_factors: string[];
  compliance_requirements: string[];
}

interface RFQDocument {
  title: string;
  description: string;
  specifications: string[];
  quantity: number;
  delivery_requirements: string;
  evaluation_criteria: string[];
  submission_deadline: string;
}

interface WorkflowResult {
  success: boolean;
  rfq_state: RFQState;
  workflow_summary: string;
  market_intelligence: MarketIntelligence;
  vendors_contacted: {
    vendors: VendorInfo[];
    total_vendors_found: number;
    perplexity_vendor_insights?: string;
  };
  rfq_document: RFQDocument;
  communication_results: {
    total_sent: number;
    successful_deliveries: number;
    failed_deliveries: number;
    delivery_results: any[];
    rfq_title: string;
    submission_deadline: string;
  };
  next_actions: string[];
  system_info: {
    workflow_id: string;
    processing_time: string;
    api_used: string;
    fallback_used: boolean;
  };
}

export function Agent() {
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<WorkflowResult | null>(null);

  const handleSubmit = async () => {
    if (!input.trim()) {
      toast.error("Please enter your RFQ request", {
        closeButton: true,
        duration: 3000
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("http://localhost:3101/api/langbase", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ input: input.trim() })
      });

      // Parse the response (with fallback for non-JSON)
      let data;
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      // Check for ANY error condition
      if (!response.ok || (data && typeof data === 'object' && (data.success === false || data.error))) {
        // Extract the most specific error message
        const errorMessage = data.error || (data.message || `Error: ${response.status}`);
        throw new Error(errorMessage);
      }

      // Process successful response
      if (typeof data === 'object' && data.success) {
        setResult(data);
        toast.success("RFQ workflow completed successfully!", {
          description: `RFQ ${data.rfq_state.rfq_id} processed and sent to ${data.communication_results.total_sent} vendors`,
          closeButton: true,
          duration: 5000
        });
      } else {
        throw new Error("Invalid response format received");
      }

    } catch (error) {
      console.error("Error processing RFQ request:", error);
      
      toast.error(error.message || "An error occurred while processing your RFQ request", {
        closeButton: true,
        duration: Infinity
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit();
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl md:text-4xl font-bold">RFQ Management System</h1>
          <p className="text-muted-foreground text-lg">
            Automated Request for Quotation processing with AI-powered market intelligence
          </p>
        </div>

        {/* Input Section */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Submit RFQ Request
            </CardTitle>
            <CardDescription>
              Describe what you need to procure. Include quantity, specifications, urgency, and any budget constraints.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="Example: I need 50 business laptops for our IT department. Budget around $50,000. Need them urgently for new employee onboarding in Bangalore."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyPress}
              className="min-h-[120px] resize-none"
              disabled={isLoading}
            />
            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                Press Ctrl+Enter to submit
              </p>
              <Button 
                onClick={handleSubmit} 
                disabled={isLoading || !input.trim()}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Process RFQ
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Section */}
        {result && (
          <div className="space-y-6">
            {/* Status Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  RFQ Status Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">RFQ ID</p>
                    <p className="text-sm text-muted-foreground font-mono">{result.rfq_state.rfq_id}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Current Stage</p>
                    <Badge variant="secondary" className="capitalize">
                      {result.rfq_state.current_stage}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Progress</p>
                    <div className="space-y-1">
                      <Progress value={result.rfq_state.progress_percentage} className="h-2" />
                      <p className="text-xs text-muted-foreground">{result.rfq_state.progress_percentage}% Complete</p>
                    </div>
                  </div>
                </div>
                
                {result.system_info.fallback_used && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Processed using fallback system. Core functionality completed successfully.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Detailed Results Tabs */}
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-2 md:grid-cols-5">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="vendors">Vendors</TabsTrigger>
                <TabsTrigger value="market">Market Intel</TabsTrigger>
                <TabsTrigger value="document">RFQ Document</TabsTrigger>
                <TabsTrigger value="actions">Next Steps</TabsTrigger>
              </TabsList>

              {/* Summary Tab */}
              <TabsContent value="summary" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Executive Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      <ReactMarkdown>{result.workflow_summary}</ReactMarkdown>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Vendors Tab */}
              <TabsContent value="vendors" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Vendor Communications ({result.communication_results.total_sent} vendors contacted)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4">
                      {result.vendors_contacted.vendors.map((vendor, index) => (
                        <div key={vendor.id} className="border rounded-lg p-4 space-y-3">
                          <div className="flex justify-between items-start">
                            <div className="space-y-1">
                              <h4 className="font-semibold">{vendor.name}</h4>
                              <p className="text-sm text-muted-foreground">{vendor.email}</p>
                            </div>
                            <div className="text-right space-y-1">
                              <div className="flex items-center gap-1">
                                <span className="text-sm">Rating:</span>
                                <Badge variant="outline">{vendor.rating}/5</Badge>
                              </div>
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <Building className="h-3 w-3" />
                                {vendor.location}
                              </div>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <p className="text-sm font-medium">Specialties:</p>
                            <div className="flex flex-wrap gap-1">
                              {vendor.specialties.map((specialty, idx) => (
                                <Badge key={idx} variant="secondary" className="text-xs">
                                  {specialty}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            RFQ sent successfully
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Market Intelligence Tab */}
              <TabsContent value="market" className="space-y-4">
                <div className="grid gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Market Intelligence
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <h4 className="font-semibold">Price Range</h4>
                        <p className="text-sm text-muted-foreground">{result.market_intelligence.price_range}</p>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <h4 className="font-semibold">Market Trends</h4>
                        <p className="text-sm text-muted-foreground">{result.market_intelligence.market_trends}</p>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <h4 className="font-semibold">Recommended Suppliers</h4>
                        <div className="flex flex-wrap gap-2">
                          {result.market_intelligence.supplier_recommendations.map((supplier, idx) => (
                            <Badge key={idx} variant="outline">{supplier}</Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5" />
                        Risk Factors & Compliance
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <h4 className="font-semibold">Risk Factors</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {result.market_intelligence.risk_factors.map((risk, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <span className="text-orange-500 mt-1">•</span>
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <h4 className="font-semibold">Compliance Requirements</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {result.market_intelligence.compliance_requirements.map((req, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <span className="text-blue-500 mt-1">•</span>
                              {req}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* RFQ Document Tab */}
              <TabsContent value="document" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Generated RFQ Document
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="font-semibold">Title</h4>
                      <p className="text-sm">{result.rfq_document.title}</p>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h4 className="font-semibold">Description</h4>
                      <p className="text-sm text-muted-foreground">{result.rfq_document.description}</p>
                    </div>
                    
                    <Separator />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-semibold">Quantity</h4>
                        <p className="text-sm">{result.rfq_document.quantity} units</p>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-semibold">Submission Deadline</h4>
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="h-4 w-4" />
                          {result.rfq_document.submission_deadline}
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h4 className="font-semibold">Technical Specifications</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {result.rfq_document.specifications.map((spec, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="text-green-500 mt-1">•</span>
                            {spec}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h4 className="font-semibold">Evaluation Criteria</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {result.rfq_document.evaluation_criteria.map((criteria, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="text-blue-500 mt-1">•</span>
                            {criteria}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h4 className="font-semibold">Delivery Requirements</h4>
                      <p className="text-sm text-muted-foreground">{result.rfq_document.delivery_requirements}</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Next Steps Tab */}
              <TabsContent value="actions" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Recommended Next Actions</CardTitle>
                    <CardDescription>
                      Follow these steps to complete your procurement process
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {result.next_actions.map((action, idx) => (
                        <div key={idx} className="flex items-start gap-3 p-3 border rounded-lg">
                          <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                            {idx + 1}
                          </div>
                          <p className="text-sm">{action}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>System Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="space-y-1">
                        <p className="font-medium">Workflow ID</p>
                        <p className="text-muted-foreground font-mono">{result.system_info.workflow_id}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="font-medium">Processing Time</p>
                        <p className="text-muted-foreground">{new Date(result.system_info.processing_time).toLocaleString()}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="font-medium">API Used</p>
                        <Badge variant="outline">{result.system_info.api_used}</Badge>
                      </div>
                      <div className="space-y-1">
                        <p className="font-medium">Fallback Mode</p>
                        <Badge variant={result.system_info.fallback_used ? "destructive" : "default"}>
                          {result.system_info.fallback_used ? "Yes" : "No"}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
}