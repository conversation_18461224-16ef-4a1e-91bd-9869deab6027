"""
Factor 12: Make Your Agent a Stateless Reducer
Implements functional programming patterns for stateless agent operations
"""

from typing import Dict, Any, List, Optional, Callable, Union, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import json
import logging
from abc import ABC, abstractmethod
from enum import Enum
import asyncio
from functools import reduce
import copy

logger = logging.getLogger(__name__)

class ActionType(Enum):
    """Supported action types for state reduction"""
    INITIALIZE = "initialize"
    UPDATE_CONTEXT = "update_context"
    ADD_MEMORY = "add_memory"
    SET_STATUS = "set_status"
    UPDATE_PROGRESS = "update_progress"
    ADD_RESULT = "add_result"
    SET_ERROR = "set_error"
    CLEAR_ERROR = "clear_error"
    MERGE_STATE = "merge_state"

@dataclass
class Action:
    """Immutable action for state reduction"""
    type: ActionType
    payload: Dict[str, Any]
    timestamp: datetime
    source: str = "system"
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class AgentState:
    """Immutable agent state"""
    execution_id: str
    status: str = "initialized"
    progress: float = 0.0
    context: Dict[str, Any] = None
    memories: List[Dict[str, Any]] = None
    results: Dict[str, Any] = None
    errors: List[str] = None
    metadata: Dict[str, Any] = None
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}
        if self.memories is None:
            self.memories = []
        if self.results is None:
            self.results = {}
        if self.errors is None:
            self.errors = []
        if self.metadata is None:
            self.metadata = {}
        if self.last_updated is None:
            self.last_updated = datetime.utcnow()

class StateReducer(ABC):
    """Abstract base class for state reducers"""
    
    @abstractmethod
    def reduce(self, state: AgentState, action: Action) -> AgentState:
        """Reduce state with action - must be pure function"""
        pass

class RFQStateReducer(StateReducer):
    """
    Factor 12: Stateless Reducer for RFQ workflows
    
    Pure functional state management with:
    - Immutable state objects
    - Pure reducer functions
    - Predictable state transitions
    - No side effects
    - Composable operations
    """
    
    def reduce(self, state: AgentState, action: Action) -> AgentState:
        """
        Pure function that returns new state based on current state and action
        No side effects, no mutations, fully predictable
        """
        # Create new state object (immutable pattern)
        new_state_dict = asdict(state)
        new_state_dict['last_updated'] = action.timestamp
        
        # Apply state transition based on action type
        if action.type == ActionType.INITIALIZE:
            return self._handle_initialize(new_state_dict, action)
        elif action.type == ActionType.UPDATE_CONTEXT:
            return self._handle_update_context(new_state_dict, action)
        elif action.type == ActionType.ADD_MEMORY:
            return self._handle_add_memory(new_state_dict, action)
        elif action.type == ActionType.SET_STATUS:
            return self._handle_set_status(new_state_dict, action)
        elif action.type == ActionType.UPDATE_PROGRESS:
            return self._handle_update_progress(new_state_dict, action)
        elif action.type == ActionType.ADD_RESULT:
            return self._handle_add_result(new_state_dict, action)
        elif action.type == ActionType.SET_ERROR:
            return self._handle_set_error(new_state_dict, action)
        elif action.type == ActionType.CLEAR_ERROR:
            return self._handle_clear_error(new_state_dict, action)
        elif action.type == ActionType.MERGE_STATE:
            return self._handle_merge_state(new_state_dict, action)
        else:
            # Return unchanged state for unknown actions
            return AgentState(**new_state_dict)
    
    def _handle_initialize(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Initialize state with payload data"""
        state_dict.update({
            'execution_id': action.payload.get('execution_id', state_dict['execution_id']),
            'status': 'initialized',
            'progress': 0.0,
            'context': action.payload.get('context', {}),
            'memories': [],
            'results': {},
            'errors': [],
            'metadata': action.payload.get('metadata', {})
        })
        return AgentState(**state_dict)
    
    def _handle_update_context(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Update context with new data (immutable merge)"""
        new_context = {**state_dict['context'], **action.payload.get('context_updates', {})}
        state_dict['context'] = new_context
        return AgentState(**state_dict)
    
    def _handle_add_memory(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Add new memory to state"""
        new_memory = {
            'content': action.payload.get('content'),
            'type': action.payload.get('memory_type', 'general'),
            'timestamp': action.timestamp.isoformat(),
            'metadata': action.payload.get('metadata', {})
        }
        state_dict['memories'] = [*state_dict['memories'], new_memory]
        return AgentState(**state_dict)
    
    def _handle_set_status(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Set new status"""
        state_dict['status'] = action.payload.get('status', state_dict['status'])
        return AgentState(**state_dict)
    
    def _handle_update_progress(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Update progress percentage"""
        new_progress = action.payload.get('progress', state_dict['progress'])
        state_dict['progress'] = max(0.0, min(1.0, new_progress))  # Clamp to [0,1]
        return AgentState(**state_dict)
    
    def _handle_add_result(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Add result data"""
        result_key = action.payload.get('key', 'default')
        result_value = action.payload.get('value')
        new_results = {**state_dict['results'], result_key: result_value}
        state_dict['results'] = new_results
        return AgentState(**state_dict)
    
    def _handle_set_error(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Add error to state"""
        error_message = action.payload.get('error', 'Unknown error')
        state_dict['errors'] = [*state_dict['errors'], error_message]
        state_dict['status'] = 'error'
        return AgentState(**state_dict)
    
    def _handle_clear_error(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Clear all errors"""
        state_dict['errors'] = []
        if state_dict['status'] == 'error':
            state_dict['status'] = 'running'
        return AgentState(**state_dict)
    
    def _handle_merge_state(self, state_dict: Dict[str, Any], action: Action) -> AgentState:
        """Merge partial state update"""
        merge_data = action.payload.get('state_updates', {})
        for key, value in merge_data.items():
            if key in state_dict:
                state_dict[key] = value
        return AgentState(**state_dict)

class StatelessAgentService:
    """
    Factor 12: Stateless Agent Service
    
    Implements stateless reducer pattern for agent operations:
    - All operations are pure functions
    - State is immutable
    - Actions are the only way to change state
    - Predictable and testable
    - Side effects isolated to service layer
    """
    
    def __init__(self):
        self.reducer = RFQStateReducer()
        self.service_id = "stateless_agent_service"
    
    # ========== PURE FUNCTIONS ==========
    
    def create_initial_state(self, execution_id: str, context: Dict[str, Any] = None) -> AgentState:
        """Create initial agent state - pure function"""
        return AgentState(
            execution_id=execution_id,
            status="initialized",
            progress=0.0,
            context=context or {},
            memories=[],
            results={},
            errors=[],
            metadata={"created_at": datetime.utcnow().isoformat()},
            last_updated=datetime.utcnow()
        )
    
    def apply_action(self, state: AgentState, action: Action) -> AgentState:
        """Apply action to state - pure function"""
        return self.reducer.reduce(state, action)
    
    def apply_actions(self, state: AgentState, actions: List[Action]) -> AgentState:
        """Apply sequence of actions to state - pure function"""
        return reduce(
            lambda current_state, action: self.apply_action(current_state, action),
            actions,
            state
        )
    
    def create_action(self, action_type: ActionType, payload: Dict[str, Any], 
                     source: str = "system") -> Action:
        """Create action object - pure function"""
        return Action(
            type=action_type,
            payload=payload,
            timestamp=datetime.utcnow(),
            source=source
        )
    
    # ========== STATE QUERY FUNCTIONS (Pure) ==========
    
    def get_status(self, state: AgentState) -> str:
        """Get current status - pure function"""
        return state.status
    
    def get_progress(self, state: AgentState) -> float:
        """Get current progress - pure function"""
        return state.progress
    
    def has_errors(self, state: AgentState) -> bool:
        """Check if state has errors - pure function"""
        return len(state.errors) > 0
    
    def get_latest_memory(self, state: AgentState, memory_type: str = None) -> Optional[Dict[str, Any]]:
        """Get latest memory of specified type - pure function"""
        filtered_memories = [
            m for m in state.memories 
            if memory_type is None or m.get('type') == memory_type
        ]
        return filtered_memories[-1] if filtered_memories else None
    
    def get_result(self, state: AgentState, key: str) -> Any:
        """Get result by key - pure function"""
        return state.results.get(key)
    
    def is_complete(self, state: AgentState) -> bool:
        """Check if workflow is complete - pure function"""
        return state.status in ['completed', 'success'] and state.progress >= 1.0
    
    def is_failed(self, state: AgentState) -> bool:
        """Check if workflow has failed - pure function"""
        return state.status == 'error' or len(state.errors) > 0
    
    # ========== STATE TRANSFORMATION FUNCTIONS (Pure) ==========
    
    def set_status(self, state: AgentState, status: str, source: str = "system") -> AgentState:
        """Set status - returns new state"""
        action = self.create_action(ActionType.SET_STATUS, {"status": status}, source)
        return self.apply_action(state, action)
    
    def update_progress(self, state: AgentState, progress: float, source: str = "system") -> AgentState:
        """Update progress - returns new state"""
        action = self.create_action(ActionType.UPDATE_PROGRESS, {"progress": progress}, source)
        return self.apply_action(state, action)
    
    def add_memory(self, state: AgentState, content: str, memory_type: str = "general", 
                  metadata: Dict[str, Any] = None, source: str = "system") -> AgentState:
        """Add memory - returns new state"""
        action = self.create_action(
            ActionType.ADD_MEMORY, 
            {
                "content": content,
                "memory_type": memory_type,
                "metadata": metadata or {}
            }, 
            source
        )
        return self.apply_action(state, action)
    
    def add_result(self, state: AgentState, key: str, value: Any, source: str = "system") -> AgentState:
        """Add result - returns new state"""
        action = self.create_action(ActionType.ADD_RESULT, {"key": key, "value": value}, source)
        return self.apply_action(state, action)
    
    def update_context(self, state: AgentState, context_updates: Dict[str, Any], 
                      source: str = "system") -> AgentState:
        """Update context - returns new state"""
        action = self.create_action(ActionType.UPDATE_CONTEXT, {"context_updates": context_updates}, source)
        return self.apply_action(state, action)
    
    def set_error(self, state: AgentState, error: str, source: str = "system") -> AgentState:
        """Set error - returns new state"""
        action = self.create_action(ActionType.SET_ERROR, {"error": error}, source)
        return self.apply_action(state, action)
    
    def clear_errors(self, state: AgentState, source: str = "system") -> AgentState:
        """Clear errors - returns new state"""
        action = self.create_action(ActionType.CLEAR_ERROR, {}, source)
        return self.apply_action(state, action)
    
    # ========== WORKFLOW ORCHESTRATION (Functional Style) ==========
    
    async def process_workflow_step(self, state: AgentState, step_name: str, 
                                  step_function: Callable, 
                                  step_input: Dict[str, Any]) -> Tuple[AgentState, Any]:
        """
        Process a workflow step using functional patterns
        Returns tuple of (new_state, step_result)
        """
        try:
            # Set status to running step
            state = self.set_status(state, f"running_{step_name}")
            
            # Execute step function (can have side effects)
            step_result = await step_function(step_input)
            
            # Update state with step result
            state = self.add_result(state, step_name, step_result)
            state = self.add_memory(state, f"Completed step: {step_name}", "workflow_step")
            
            # Update progress (assume each step is equal weight)
            current_progress = state.progress
            new_progress = min(1.0, current_progress + 0.1)  # Increment by 10%
            state = self.update_progress(state, new_progress)
            
            return state, step_result
            
        except Exception as e:
            # Handle error functionally
            error_message = f"Step {step_name} failed: {str(e)}"
            state = self.set_error(state, error_message)
            return state, None
    
    def compose_workflow_steps(self, *step_functions: Callable) -> Callable:
        """
        Compose multiple workflow steps into a single function
        Functional composition pattern
        """
        async def composed_workflow(initial_state: AgentState, workflow_input: Dict[str, Any]) -> AgentState:
            current_state = initial_state
            current_input = workflow_input
            
            for i, step_function in enumerate(step_functions):
                step_name = f"step_{i+1}"
                current_state, step_result = await self.process_workflow_step(
                    current_state, step_name, step_function, current_input
                )
                
                # If step failed, stop execution
                if self.has_errors(current_state):
                    break
                
                # Pass step result as input to next step
                current_input = {"previous_result": step_result, **current_input}
            
            # Set final status
            if self.has_errors(current_state):
                current_state = self.set_status(current_state, "failed")
            else:
                current_state = self.set_status(current_state, "completed")
                current_state = self.update_progress(current_state, 1.0)
            
            return current_state
        
        return composed_workflow
    
    # ========== STATE SERIALIZATION (Pure) ==========
    
    def serialize_state(self, state: AgentState) -> Dict[str, Any]:
        """Serialize state to dictionary - pure function"""
        state_dict = asdict(state)
        # Convert datetime to ISO string
        state_dict['last_updated'] = state.last_updated.isoformat()
        return state_dict
    
    def deserialize_state(self, state_dict: Dict[str, Any]) -> AgentState:
        """Deserialize state from dictionary - pure function"""
        # Convert ISO string back to datetime
        state_dict = copy.deepcopy(state_dict)
        if isinstance(state_dict.get('last_updated'), str):
            state_dict['last_updated'] = datetime.fromisoformat(state_dict['last_updated'])
        
        return AgentState(**state_dict)
    
    # ========== HIGHER-ORDER FUNCTIONS ==========
    
    def map_over_state_collection(self, states: List[AgentState], 
                                 transform_func: Callable[[AgentState], AgentState]) -> List[AgentState]:
        """Apply transformation function to collection of states - pure function"""
        return [transform_func(state) for state in states]
    
    def filter_states(self, states: List[AgentState], 
                     predicate: Callable[[AgentState], bool]) -> List[AgentState]:
        """Filter states by predicate - pure function"""
        return [state for state in states if predicate(state)]
    
    def reduce_states(self, states: List[AgentState], 
                     reducer_func: Callable[[AgentState, AgentState], AgentState]) -> Optional[AgentState]:
        """Reduce collection of states to single state - pure function"""
        if not states:
            return None
        return reduce(reducer_func, states[1:], states[0])
    
    # ========== VALIDATION FUNCTIONS (Pure) ==========
    
    def validate_state_transition(self, from_state: AgentState, to_state: AgentState) -> bool:
        """Validate that state transition is valid - pure function"""
        # Basic validation rules
        if to_state.last_updated < from_state.last_updated:
            return False
        
        if to_state.progress < from_state.progress:
            return False
        
        # Status transition validation
        valid_transitions = {
            'initialized': ['running', 'error'],
            'running': ['paused', 'completed', 'error', 'running'],
            'paused': ['running', 'error'],
            'completed': ['completed'],
            'error': ['running', 'error']
        }
        
        from_status = from_state.status.split('_')[0]  # Handle "running_step1" -> "running"
        to_status = to_state.status.split('_')[0]
        
        return to_status in valid_transitions.get(from_status, [])
    
    def validate_action(self, action: Action) -> bool:
        """Validate action is well-formed - pure function"""
        if not isinstance(action.type, ActionType):
            return False
        
        if not isinstance(action.payload, dict):
            return False
        
        if not isinstance(action.timestamp, datetime):
            return False
        
        return True
    
    def get_service_capabilities(self) -> Dict[str, Any]:
        """Return service capabilities"""
        return {
            "service_id": self.service_id,
            "service_type": "stateless_reducer",
            "capabilities": [
                "immutable_state_management",
                "pure_function_operations",
                "action_based_state_transitions",
                "functional_composition",
                "state_serialization",
                "workflow_orchestration"
            ],
            "action_types": [action_type.value for action_type in ActionType],
            "description": "Stateless reducer service implementing functional programming patterns",
            "version": "1.0.0",
            "factor_compliance": ["Factor 12: Make Your Agent a Stateless Reducer"],
            "guarantees": [
                "No side effects in pure functions",
                "Immutable state objects", 
                "Predictable state transitions",
                "Composable operations",
                "Testable components"
            ]
        }