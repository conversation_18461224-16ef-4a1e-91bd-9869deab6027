events {
    worker_connections 1024;
}

http {
    upstream agent_backend {
        least_conn;
        server agent-service:8000 max_fails=3 fail_timeout=30s;
        server langgraph-orchestrator:8002 max_fails=3 fail_timeout=30s backup;
    }

    upstream memory_backend {
        server memory-service:8003 max_fails=3 fail_timeout=30s;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=memory:10m rate=5r/s;

    server {
        listen 80;
        server_name _;

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://agent_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Memory service routes
        location /memory/ {
            limit_req zone=memory burst=10 nodelay;
            proxy_pass http://memory_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
