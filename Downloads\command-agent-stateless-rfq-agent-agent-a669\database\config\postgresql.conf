# database/config/postgresql.conf
# PostgreSQL configuration optimized for AI agent workloads

# ========== CONNECTION SETTINGS ==========
max_connections = 200
shared_preload_libraries = 'pg_stat_statements,vector'

# ========== MEMORY SETTINGS ==========
shared_buffers = 2GB                    # 25% of RAM for 8GB system
effective_cache_size = 6GB              # 75% of RAM
work_mem = 64MB                         # For sorting and hash operations
maintenance_work_mem = 512MB            # For VACUUM, CREATE INDEX, etc.
huge_pages = try

# ========== WRITE AHEAD LOG ==========
wal_level = replica
max_wal_size = 4GB
min_wal_size = 1GB
checkpoint_completion_target = 0.9
wal_buffers = 64MB

# ========== QUERY PLANNER ==========
random_page_cost = 1.1                 # SSD-optimized
effective_io_concurrency = 200         # SSD-optimized
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4

# ========== VECTOR EXTENSION SETTINGS ==========
# pgvector specific settings for AI embeddings
shared_preload_libraries = 'vector'

# ========== LOGGING ==========
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000      # Log slow queries
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_statement = 'ddl'

# ========== PERFORMANCE MONITORING ==========
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
shared_preload_libraries = 'pg_stat_statements'

# ========== AUTOVACUUM ==========
autovacuum = on
autovacuum_max_workers = 6
autovacuum_naptime = 30s
autovacuum_vacuum_threshold = 500
autovacuum_analyze_threshold = 250
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_scale_factor = 0.05

# ========== REPLICATION ==========
hot_standby = on
max_standby_streaming_delay = 30s
wal_receiver_timeout = 60s

# ========== SECURITY ==========
ssl = on
ssl_cert_file = '/etc/ssl/certs/server.crt'
ssl_key_file = '/etc/ssl/private/server.key'
password_encryption = scram-sha-256

# ========== TIMEZONE ==========
timezone = 'UTC'
log_timezone = 'UTC'

# ========== LOCALE ==========
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'
default_text_search_config = 'pg_catalog.english'