"""
Comprehensive Test Suite for 12-Factor Agent Implementation
Tests all factors with real scenarios and edge cases
"""

import pytest
import asyncio
import json
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from .conftest import TestBase, MockPerplexityService


class Test12FactorAgentImplementation(TestBase):
    """Comprehensive tests for all 12 factors"""
    
    # ========== FACTOR 1: NATURAL LANGUAGE TO TOOL CALLS ==========
    
    @pytest.mark.factor1
    @pytest.mark.unit
    async def test_factor1_perplexity_integration(self, test_services, mock_perplexity):
        """Test Factor 1: Natural Language to Tool Calls"""
        perplexity_service = test_services['perplexity']
        
        # Mock the Perplexity service
        with patch.object(perplexity_service, 'research_with_sources', mock_perplexity.research_with_sources):
            result = await perplexity_service.research_with_sources(
                "Research market conditions for laptops in India"
            )
            
            self.assert_successful_response({"success": True})
            assert result["content"] is not None
            assert "sources" in result
            assert result["confidence"] > 0
    
    @pytest.mark.factor1
    @pytest.mark.integration
    async def test_factor1_tool_call_workflow(self, test_services, sample_rfq_request):
        """Test complete tool call workflow"""
        from services.agent.services.tool_service import ToolService
        
        tool_service = ToolService()
        
        # Test tool registration
        tools = await tool_service.get_active_tools()
        assert isinstance(tools, list)
        
        # Test structured tool output
        tool_result = {
            "tool_name": "market_research",
            "output": {"market_data": "test", "confidence": 0.9},
            "execution_time": 1.5
        }
        
        # Validate structured output
        assert "tool_name" in tool_result
        assert "output" in tool_result
        assert isinstance(tool_result["output"], dict)
    
    # ========== FACTOR 2: OWN YOUR PROMPTS ==========
    
    @pytest.mark.factor2
    @pytest.mark.unit
    async def test_factor2_prompt_management(self, test_services, test_database):
        """Test Factor 2: Own Your Prompts"""
        from services.agent.services.prompt_service import PromptService
        
        prompt_service = PromptService()
        
        # Test prompt creation
        prompt_data = {
            "name": "test_prompt",
            "template": "Research {topic} in {region}",
            "version": "1.0.0",
            "variables": ["topic", "region"]
        }
        
        # Test prompt versioning
        created_prompt = await prompt_service.create_prompt(prompt_data, test_database)
        assert created_prompt["name"] == "test_prompt"
        assert created_prompt["version"] == "1.0.0"
        
        # Test prompt retrieval
        retrieved_prompt = await prompt_service.get_prompt("test_prompt", test_database)
        assert retrieved_prompt["template"] == prompt_data["template"]
    
    @pytest.mark.factor2
    @pytest.mark.integration
    async def test_factor2_prompt_versioning(self, test_services, test_database):
        """Test prompt versioning system"""
        from services.agent.services.prompt_service import PromptService
        
        prompt_service = PromptService()
        
        # Create multiple versions
        base_prompt = {
            "name": "market_research_prompt",
            "template": "Research {topic}",
            "version": "1.0.0"
        }
        
        updated_prompt = {
            "name": "market_research_prompt", 
            "template": "Research {topic} in {region} with focus on {criteria}",
            "version": "2.0.0"
        }
        
        await prompt_service.create_prompt(base_prompt, test_database)
        await prompt_service.create_prompt(updated_prompt, test_database)
        
        # Test getting latest version
        latest = await prompt_service.get_prompt("market_research_prompt", test_database)
        assert latest["version"] == "2.0.0"
    
    # ========== FACTOR 3: OWN YOUR CONTEXT WINDOW ==========
    
    @pytest.mark.factor3
    @pytest.mark.unit
    async def test_factor3_memory_storage(self, test_services, test_database):
        """Test Factor 3: Own Your Context Window"""
        memory_service = test_services['memory']
        
        # Test memory storage
        await memory_service.store_conversation_memory(
            agent_id="test_agent",
            content="Test memory content for RFQ workflow",
            memory_type="workflow_memory",
            metadata={"category": "IT", "region": "India"}
        )
        
        # Test memory search
        results = await memory_service.search_memories(
            query="RFQ workflow",
            agent_id="test_agent",
            limit=5
        )
        
        assert len(results) > 0
        assert "RFQ workflow" in results[0].content
    
    @pytest.mark.factor3
    @pytest.mark.integration
    async def test_factor3_vector_similarity_search(self, test_services, test_database):
        """Test vector similarity search"""
        memory_service = test_services['memory']
        
        # Store multiple related memories
        memories = [
            "Research laptop suppliers in Mumbai region",
            "Vendor discovery for IT equipment procurement", 
            "Market analysis for business hardware needs",
            "Unrelated content about office furniture"
        ]
        
        for i, content in enumerate(memories):
            await memory_service.store_conversation_memory(
                agent_id="test_agent",
                content=content,
                memory_type="test_memory",
                metadata={"index": i}
            )
        
        # Search should find IT-related content
        results = await memory_service.search_memories(
            query="laptop procurement suppliers",
            agent_id="test_agent",
            limit=3
        )
        
        assert len(results) >= 2
        # Should prioritize laptop/IT related content
        assert any("laptop" in result.content.lower() for result in results)
    
    # ========== FACTOR 4: TOOLS ARE STRUCTURED OUTPUTS ==========
    
    @pytest.mark.factor4
    @pytest.mark.unit
    async def test_factor4_structured_output_validation(self, test_services):
        """Test Factor 4: Tools Are Just Structured Outputs"""
        from services.agent.services.tool_service import ToolService
        
        tool_service = ToolService()
        
        # Test structured tool output
        structured_output = {
            "market_research": {
                "price_range": "$800-1200",
                "suppliers": ["Dell", "HP", "Lenovo"],
                "market_trends": "Growing demand",
                "confidence_score": 0.85
            },
            "metadata": {
                "execution_time": 2.3,
                "data_sources": ["market_db", "supplier_db"],
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        # Validate structure
        assert "market_research" in structured_output
        assert "metadata" in structured_output
        assert "confidence_score" in structured_output["market_research"]
        assert isinstance(structured_output["market_research"]["suppliers"], list)
    
    @pytest.mark.factor4
    @pytest.mark.integration
    async def test_factor4_tool_output_schema_validation(self, test_services):
        """Test tool output schema validation"""
        from pydantic import BaseModel, ValidationError
        from typing import List, Optional
        
        class MarketResearchOutput(BaseModel):
            price_range: str
            suppliers: List[str]
            confidence_score: float
            market_trends: Optional[str] = None
        
        # Valid output
        valid_output = {
            "price_range": "$800-1200",
            "suppliers": ["Dell", "HP"],
            "confidence_score": 0.85,
            "market_trends": "Growing"
        }
        
        # Should validate successfully
        validated = MarketResearchOutput(**valid_output)
        assert validated.price_range == "$800-1200"
        assert len(validated.suppliers) == 2
        
        # Invalid output should fail
        invalid_output = {
            "price_range": "$800-1200",
            "suppliers": "Dell",  # Should be list
            "confidence_score": "high"  # Should be float
        }
        
        with pytest.raises(ValidationError):
            MarketResearchOutput(**invalid_output)
    
    # ========== FACTOR 5: UNIFY EXECUTION STATE AND BUSINESS STATE ==========
    
    @pytest.mark.factor5
    @pytest.mark.unit
    async def test_factor5_state_unification(self, test_services, test_database):
        """Test Factor 5: Unify Execution State and Business State"""
        from services.agent.database.models import AgentExecution, WorkflowState
        
        # Test state storage
        execution_state = {
            "execution_id": "test_execution_123",
            "workflow_type": "rfq_processing",
            "current_step": "market_research",
            "progress": 0.4,
            "business_data": {
                "rfq_id": "RFQ-001",
                "vendor_count": 15,
                "budget_range": "$50000"
            }
        }
        
        # Verify state structure includes both execution and business data
        assert "execution_id" in execution_state
        assert "business_data" in execution_state
        assert "progress" in execution_state
        assert execution_state["business_data"]["rfq_id"] == "RFQ-001"
    
    @pytest.mark.factor5
    @pytest.mark.integration
    async def test_factor5_postgresql_persistence(self, test_database):
        """Test PostgreSQL state persistence"""
        # Test database connection and state persistence
        query = "SELECT 1 as test_value"
        result = await test_database.fetch_one(query)
        assert result["test_value"] == 1
        
        # Test state table operations would go here
        # (Simplified for test environment)
    
    # ========== FACTOR 6: LAUNCH/PAUSE/RESUME ==========
    
    @pytest.mark.factor6
    @pytest.mark.unit
    async def test_factor6_checkpoint_creation(self, test_services):
        """Test Factor 6: Launch/Pause/Resume"""
        from services.agent.services.checkpoint_service import CheckpointService
        
        checkpoint_service = CheckpointService()
        
        # Test checkpoint creation
        workflow_state = {
            "execution_id": "test_workflow_123",
            "current_step": "vendor_discovery",
            "progress": 0.6,
            "intermediate_results": {
                "market_research": {"completed": True},
                "vendor_list": ["vendor1", "vendor2"]
            }
        }
        
        checkpoint_id = await checkpoint_service.create_checkpoint(
            execution_id="test_workflow_123",
            state_data=workflow_state,
            step_name="vendor_discovery"
        )
        
        assert checkpoint_id is not None
        assert len(checkpoint_id) > 0
    
    @pytest.mark.factor6
    @pytest.mark.integration
    async def test_factor6_pause_resume_workflow(self, test_services):
        """Test workflow pause and resume functionality"""
        from services.agent.services.checkpoint_service import CheckpointService
        
        checkpoint_service = CheckpointService()
        execution_id = "test_pause_resume_123"
        
        # Create initial checkpoint
        initial_state = {
            "execution_id": execution_id,
            "status": "running",
            "current_step": "market_research",
            "progress": 0.3
        }
        
        checkpoint_id = await checkpoint_service.create_checkpoint(
            execution_id=execution_id,
            state_data=initial_state,
            step_name="market_research"
        )
        
        # Test pause
        paused_state = await checkpoint_service.pause_workflow(execution_id)
        assert paused_state["status"] == "paused"
        
        # Test resume
        resumed_state = await checkpoint_service.resume_workflow(execution_id)
        assert resumed_state["status"] == "running"
        assert resumed_state["current_step"] == "market_research"
    
    @pytest.mark.factor6
    @pytest.mark.integration
    async def test_factor6_checkpoint_recovery(self, test_services):
        """Test checkpoint-based recovery"""
        from services.agent.services.checkpoint_service import CheckpointService
        
        checkpoint_service = CheckpointService()
        execution_id = "test_recovery_123"
        
        # Create multiple checkpoints
        checkpoints_data = [
            {"step": "parse_input", "progress": 0.1, "data": {"parsed": True}},
            {"step": "market_research", "progress": 0.4, "data": {"research_done": True}},
            {"step": "vendor_discovery", "progress": 0.7, "data": {"vendors_found": 15}}
        ]
        
        checkpoint_ids = []
        for checkpoint_data in checkpoints_data:
            checkpoint_id = await checkpoint_service.create_checkpoint(
                execution_id=execution_id,
                state_data=checkpoint_data,
                step_name=checkpoint_data["step"]
            )
            checkpoint_ids.append(checkpoint_id)
        
        # Test recovery to latest checkpoint
        recovered_state = await checkpoint_service.recover_from_checkpoint(execution_id)
        assert recovered_state["step"] == "vendor_discovery"
        assert recovered_state["progress"] == 0.7
        assert recovered_state["data"]["vendors_found"] == 15