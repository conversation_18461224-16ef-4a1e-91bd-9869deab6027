groups:
  - name: ai_agent_alerts
    rules:
      # Service Health Alerts
      - alert: AgentServiceDown
        expr: up{job="ai-agent-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AI Agent Service is down"
          description: "AI Agent Service has been down for more than 1 minute"

      - alert: DatabaseDown
        expr: up{job="postgresql"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database has been unreachable for more than 30 seconds"

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: warning
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache has been unreachable for more than 30 seconds"

      # Performance Alerts
      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is above 5 seconds for {{ $labels.job }}"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 10% for {{ $labels.job }}"

      # Resource Usage Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 85% on {{ $labels.instance }}"

      - alert: LowDiskSpace
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk usage is above 90% on {{ $labels.instance }}"

      # Application-Specific Alerts
      - alert: FailedWorkflows
        expr: increase(langgraph_workflow_failures_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High workflow failure rate"
          description: "More than 5 workflow failures in the last 5 minutes"

      - alert: LongRunningWorkflows
        expr: langgraph_workflow_duration_seconds > 300
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Long running workflow detected"
          description: "Workflow {{ $labels.workflow_id }} has been running for more than 5 minutes"

      - alert: MemoryConsolidationFailures
        expr: increase(memory_consolidation_failures_total[10m]) > 3
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Memory consolidation failures"
          description: "More than 3 memory consolidation failures in the last 10 minutes"

      - alert: HighQueueDepth
        expr: rabbitmq_queue_messages{queue=~".*"} > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High queue depth in RabbitMQ"
          description: "Queue {{ $labels.queue }} has more than 1000 messages"

      # Database Alerts
      - alert: PostgreSQLConnectionsHigh
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL connections high"
          description: "PostgreSQL connections are above 80% of maximum"

      - alert: PostgreSQLSlowQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "Queries running longer than 5 minutes detected"

      # Vector Database Alerts
      - alert: VectorSearchPerformance
        expr: pgvector_search_duration_seconds{quantile="0.95"} > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Slow vector search performance"
          description: "Vector search 95th percentile is above 2 seconds"

  - name: business_logic_alerts
    rules:
      # RFQ-Specific Business Logic Alerts
      - alert: LowVendorDiscoveryRate
        expr: avg_over_time(vendor_discovery_success_rate[30m]) < 0.8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low vendor discovery success rate"
          description: "Vendor discovery success rate is below 80% over the last 30 minutes"

      - alert: MarketResearchFailures
        expr: increase(market_research_failures_total[15m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High market research failure rate"
          description: "More than 10 market research failures in the last 15 minutes"

      - alert: DocumentGenerationBacklog
        expr: document_generation_queue_size > 50
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Document generation backlog"
          description: "Document generation queue has more than 50 pending requests"

      - alert: HumanApprovalTimeout
        expr: human_approval_pending_duration_seconds > 86400
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Human approval timeout"
          description: "Human approval task {{ $labels.task_id }} has been pending for more than 24 hours"