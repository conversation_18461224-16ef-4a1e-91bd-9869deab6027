#!/usr/bin/env python3
"""
Comprehensive Test Runner for LangGraph AI Agent System
Supports different test categories and reporting options
"""

import subprocess
import sys
import os
import argparse
from pathlib import Path
import time
from typing import List, Optional


class TestRunner:
    """Comprehensive test runner with multiple options"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_dir = self.project_root / "tests"
        
    def run_command(self, cmd: List[str], description: str) -> bool:
        """Run a command and return success status"""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(cmd)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root)
        end_time = time.time()
        
        duration = end_time - start_time
        if result.returncode == 0:
            print(f"\n✅ {description} completed successfully in {duration:.2f}s")
            return True
        else:
            print(f"\n❌ {description} failed after {duration:.2f}s")
            return False
    
    def run_unit_tests(self) -> bool:
        """Run unit tests only"""
        cmd = [
            sys.executable, "-m", "pytest",
            "-m", "unit",
            "--verbose",
            "--tb=short"
        ]
        return self.run_command(cmd, "Unit Tests")
    
    def run_integration_tests(self) -> bool:
        """Run integration tests only"""
        cmd = [
            sys.executable, "-m", "pytest", 
            "-m", "integration",
            "--verbose",
            "--tb=short"
        ]
        return self.run_command(cmd, "Integration Tests")
    
    def run_e2e_tests(self) -> bool:
        """Run end-to-end tests only"""
        cmd = [
            sys.executable, "-m", "pytest",
            "-m", "e2e", 
            "--verbose",
            "--tb=long"
        ]
        return self.run_command(cmd, "End-to-End Tests")
    
    def run_factor_tests(self, factor_number: int) -> bool:
        """Run tests for specific factor"""
        cmd = [
            sys.executable, "-m", "pytest",
            "-m", f"factor{factor_number}",
            "--verbose"
        ]
        return self.run_command(cmd, f"Factor {factor_number} Tests")
    
    def run_all_factor_tests(self) -> bool:
        """Run tests for all 12 factors"""
        success_count = 0
        
        for factor in range(1, 13):
            print(f"\n🔍 Testing Factor {factor}")
            if self.run_factor_tests(factor):
                success_count += 1
            else:
                print(f"❌ Factor {factor} tests failed")
        
        print(f"\n📊 Factor Tests Summary: {success_count}/12 factors passed")
        return success_count == 12
    
    def run_performance_tests(self) -> bool:
        """Run performance-focused tests"""
        cmd = [
            sys.executable, "-m", "pytest",
            "-m", "slow",
            "--verbose",
            "--durations=10"
        ]
        return self.run_command(cmd, "Performance Tests")
    
    def run_with_coverage(self) -> bool:
        """Run all tests with coverage report"""
        cmd = [
            sys.executable, "-m", "pytest",
            "--cov=services/agent",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-report=xml",
            "--verbose"
        ]
        return self.run_command(cmd, "All Tests with Coverage")
    
    def run_quick_tests(self) -> bool:
        """Run quick test suite (unit tests only)"""
        cmd = [
            sys.executable, "-m", "pytest",
            "-m", "unit and not slow",
            "--verbose",
            "--tb=line",
            "--maxfail=5"
        ]
        return self.run_command(cmd, "Quick Test Suite")
    
    def run_comprehensive_tests(self) -> bool:
        """Run comprehensive test suite"""
        print("\n🚀 Starting Comprehensive Test Suite")
        print("This includes all test types with detailed reporting")
        
        success_count = 0
        total_test_types = 4
        
        # 1. Unit Tests
        if self.run_unit_tests():
            success_count += 1
        
        # 2. Integration Tests  
        if self.run_integration_tests():
            success_count += 1
        
        # 3. All Factor Tests
        if self.run_all_factor_tests():
            success_count += 1
        
        # 4. Coverage Report
        if self.run_with_coverage():
            success_count += 1
        
        print(f"\n📋 Comprehensive Test Summary:")
        print(f"   ✅ Successful test categories: {success_count}/{total_test_types}")
        
        if success_count == total_test_types:
            print(f"   🎉 All tests passed! System is ready for deployment.")
            return True
        else:
            print(f"   ⚠️  Some test categories failed. Review results above.")
            return False
    
    def run_api_tests(self) -> bool:
        """Run API integration tests"""
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_api_integration.py",
            "--verbose"
        ]
        return self.run_command(cmd, "API Integration Tests")
    
    def run_specific_test_file(self, test_file: str) -> bool:
        """Run specific test file"""
        cmd = [
            sys.executable, "-m", "pytest",
            test_file,
            "--verbose"
        ]
        return self.run_command(cmd, f"Test File: {test_file}")
    
    def setup_test_environment(self) -> bool:
        """Set up test environment"""
        print("🔧 Setting up test environment...")
        
        # Set environment variables for testing
        os.environ.update({
            "ENVIRONMENT": "testing",
            "LOG_LEVEL": "DEBUG",
            "DATABASE_URL": "sqlite:///test.db",
            "REDIS_URL": "redis://localhost:6379/15",
            "PERPLEXITY_API_KEY": "test-key-12345"
        })
        
        # Check if pytest is installed
        try:
            import pytest
            print(f"✅ Pytest version: {pytest.__version__}")
        except ImportError:
            print("❌ Pytest not installed. Run: pip install pytest pytest-asyncio pytest-cov")
            return False
        
        # Check test directory exists
        if not self.test_dir.exists():
            print(f"❌ Test directory not found: {self.test_dir}")
            return False
        
        print("✅ Test environment setup complete")
        return True
    
    def generate_test_report(self) -> bool:
        """Generate comprehensive test report"""
        cmd = [
            sys.executable, "-m", "pytest",
            "--html=test_report.html",
            "--self-contained-html",
            "--verbose"
        ]
        return self.run_command(cmd, "Test Report Generation")


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="LangGraph AI Agent Test Runner")
    
    parser.add_argument(
        "command",
        choices=[
            "unit", "integration", "e2e", "factor", "factors", 
            "performance", "coverage", "quick", "comprehensive",
            "api", "setup", "report"
        ],
        help="Test command to run"
    )
    
    parser.add_argument(
        "--factor",
        type=int,
        choices=range(1, 13),
        help="Specific factor to test (1-12)"
    )
    
    parser.add_argument(
        "--file",
        type=str,
        help="Specific test file to run"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Setup test environment first
    if not runner.setup_test_environment():
        print("❌ Failed to setup test environment")
        sys.exit(1)
    
    success = False
    
    if args.command == "setup":
        success = True  # Already done above
    elif args.command == "unit":
        success = runner.run_unit_tests()
    elif args.command == "integration":
        success = runner.run_integration_tests()
    elif args.command == "e2e":
        success = runner.run_e2e_tests()
    elif args.command == "factor":
        if args.factor:
            success = runner.run_factor_tests(args.factor)
        else:
            print("❌ --factor argument required for factor command")
            sys.exit(1)
    elif args.command == "factors":
        success = runner.run_all_factor_tests()
    elif args.command == "performance":
        success = runner.run_performance_tests()
    elif args.command == "coverage":
        success = runner.run_with_coverage()
    elif args.command == "quick":
        success = runner.run_quick_tests()
    elif args.command == "comprehensive":
        success = runner.run_comprehensive_tests()
    elif args.command == "api":
        success = runner.run_api_tests()
    elif args.command == "report":
        success = runner.generate_test_report()
    
    if args.file:
        success = runner.run_specific_test_file(args.file)
    
    if success:
        print(f"\n🎉 Test command '{args.command}' completed successfully!")
        sys.exit(0)
    else:
        print(f"\n❌ Test command '{args.command}' failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()