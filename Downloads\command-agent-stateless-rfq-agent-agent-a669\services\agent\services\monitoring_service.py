"""
Comprehensive Monitoring Service
Implements metrics collection for all 12-Factor Agent components
"""

from prometheus_client import Counter, Histogram, Gauge, Summary, generate_latest, CONTENT_TYPE_LATEST
from typing import Dict, Any, Optional
import time
import logging
from datetime import datetime
from functools import wraps

logger = logging.getLogger(__name__)

class MetricsCollector:
    """
    Centralized metrics collection for the AI Agent system
    Tracks all 12-Factor Agent components with detailed metrics
    """
    
    def __init__(self):
        self.setup_metrics()
    
    def setup_metrics(self):
        """Initialize all Prometheus metrics"""
        
        # ========== GENERAL SYSTEM METRICS ==========
        self.http_requests_total = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status']
        )
        
        self.http_request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint']
        )
        
        # ========== FACTOR 1: NATURAL LANGUAGE TO TOOL CALLS ==========
        self.tool_calls_total = Counter(
            'tool_calls_total',
            'Total tool calls made',
            ['tool_name', 'status']
        )
        
        self.tool_call_duration = Histogram(
            'tool_call_duration_seconds',
            'Tool call execution time',
            ['tool_name']
        )
        
        self.perplexity_api_calls = Counter(
            'perplexity_api_calls_total',
            'Total Perplexity API calls',
            ['operation_type', 'status']
        )
        
        # ========== FACTOR 2: OWN YOUR PROMPTS ==========
        self.prompt_retrievals = Counter(
            'prompt_retrievals_total',
            'Total prompt retrievals',
            ['prompt_name', 'version']
        )
        
        self.prompt_cache_hits = Counter(
            'prompt_cache_hits_total',
            'Prompt cache hits'
        )
        
        # ========== FACTOR 3: OWN YOUR CONTEXT WINDOW ==========
        self.memory_searches = Counter(
            'memory_searches_total',
            'Total memory searches',
            ['search_type']
        )
        
        self.memory_search_duration = Histogram(
            'memory_search_duration_seconds',
            'Memory search duration',
            ['search_type']
        )
        
        self.memory_consolidations = Counter(
            'memory_consolidation_operations_total',
            'Memory consolidation operations',
            ['status']
        )
        
        self.context_window_size = Histogram(
            'context_window_size_tokens',
            'Context window size in tokens',
            ['operation']
        )
        
        # ========== FACTOR 4: TOOLS ARE STRUCTURED OUTPUTS ==========
        self.tool_registrations = Counter(
            'tool_registrations_total',
            'Tool registrations',
            ['tool_type']
        )
        
        self.structured_output_validations = Counter(
            'structured_output_validations_total',
            'Structured output validations',
            ['tool_name', 'status']
        )
        
        # ========== FACTOR 5: UNIFY EXECUTION STATE ==========
        self.state_updates = Counter(
            'agent_state_updates_total',
            'Agent state updates',
            ['state_type']
        )
        
        self.database_operations = Counter(
            'database_operations_total',
            'Database operations',
            ['operation_type', 'table', 'status']
        )
        
        # ========== FACTOR 6: LAUNCH/PAUSE/RESUME ==========
        self.workflow_checkpoints = Counter(
            'workflow_checkpoints_total',
            'Workflow checkpoints created',
            ['workflow_type']
        )
        
        self.workflow_pauses = Counter(
            'workflow_pauses_total',
            'Workflow pauses',
            ['workflow_type']
        )
        
        self.workflow_resumes = Counter(
            'workflow_resumes_total',
            'Workflow resumes',
            ['workflow_type']
        )
        
        # ========== FACTOR 7: CONTACT HUMANS ==========
        self.human_tasks_created = Counter(
            'human_tasks_created_total',
            'Human tasks created',
            ['task_type']
        )
        
        self.human_tasks_completed = Counter(
            'human_tasks_completed_total',
            'Human tasks completed',
            ['task_type']
        )
        
        self.human_task_duration = Histogram(
            'human_task_duration_seconds',
            'Human task completion time',
            ['task_type']
        )
        
        self.human_tasks_pending = Gauge(
            'human_tasks_pending_total',
            'Currently pending human tasks'
        )
        
        # ========== FACTOR 8: OWN YOUR CONTROL FLOW ==========
        self.langgraph_workflows = Counter(
            'langgraph_workflow_executions_total',
            'LangGraph workflow executions',
            ['workflow_type', 'status']
        )
        
        self.langgraph_workflow_duration = Histogram(
            'langgraph_workflow_duration_seconds',
            'LangGraph workflow duration',
            ['workflow_type']
        )
        
        self.workflow_steps = Counter(
            'workflow_steps_total',
            'Workflow steps executed',
            ['workflow_type', 'step_name', 'status']
        )
        
        # ========== FACTOR 9: COMPACT ERRORS ==========
        self.error_compactions = Counter(
            'error_manager_compactions_total',
            'Error compactions performed'
        )
        
        self.error_pattern_matches = Counter(
            'error_manager_pattern_matches_total',
            'Error pattern matches found'
        )
        
        self.error_context_size = Histogram(
            'error_context_size_chars',
            'Error context size after compaction'
        )
        
        # ========== FACTOR 10: SMALL FOCUSED AGENTS ==========
        self.agent_executions = Counter(
            'agent_executions_total',
            'Agent executions',
            ['agent_type', 'status']
        )
        
        self.agent_coordination = Counter(
            'multi_agent_coordinations_total',
            'Multi-agent coordinations',
            ['coordination_type']
        )
        
        self.agent_quality_scores = Histogram(
            'agent_quality_scores',
            'Agent execution quality scores',
            ['agent_type']
        )
        
        # ========== FACTOR 11: TRIGGER FROM ANYWHERE ==========
        self.input_requests = Counter(
            'input_requests_total',
            'Input requests processed',
            ['source', 'format', 'status']
        )
        
        self.input_processing_duration = Histogram(
            'input_processing_duration_seconds',
            'Input processing duration',
            ['source', 'format']
        )
        
        self.intent_classifications = Counter(
            'intent_classifications_total',
            'Intent classifications',
            ['intent', 'confidence_level']
        )
        
        # ========== FACTOR 12: STATELESS REDUCER ==========
        self.state_reductions = Counter(
            'state_reductions_total',
            'State reductions performed',
            ['action_type']
        )
        
        self.functional_operations = Counter(
            'functional_operations_total',
            'Functional operations executed',
            ['operation_type', 'status']
        )
        
        self.state_serializations = Counter(
            'state_serializations_total',
            'State serializations',
            ['serialization_type']
        )
        
        # ========== VECTOR DATABASE METRICS ==========
        self.pgvector_searches = Counter(
            'pgvector_searches_total',
            'PGVector searches performed'
        )
        
        self.pgvector_search_duration = Histogram(
            'pgvector_search_duration_seconds',
            'PGVector search duration'
        )
        
        self.pgvector_index_size = Gauge(
            'pgvector_index_size_bytes',
            'PGVector index size in bytes'
        )
        
        # ========== BUSINESS METRICS ==========
        self.rfq_completions = Counter(
            'rfq_completions_total',
            'RFQ workflow completions',
            ['status']
        )
        
        self.vendor_discoveries = Counter(
            'vendor_discoveries_total',
            'Vendor discoveries',
            ['vendor_tier']
        )
        
        self.document_generations = Counter(
            'document_generations_total',
            'Document generations',
            ['document_type', 'status']
        )
        
        self.market_research_operations = Counter(
            'market_research_operations_total',
            'Market research operations',
            ['research_type', 'status']
        )
        
        # ========== PERFORMANCE GAUGES ==========
        self.active_workflows = Gauge(
            'active_workflows_current',
            'Currently active workflows'
        )
        
        self.memory_usage_bytes = Gauge(
            'memory_usage_bytes',
            'Memory usage in bytes',
            ['component']
        )
        
        self.queue_depth = Gauge(
            'queue_depth_current',
            'Current queue depth',
            ['queue_name']
        )
    
    # ========== METRIC RECORDING METHODS ==========
    
    def record_http_request(self, method: str, endpoint: str, status: int, duration: float):
        """Record HTTP request metrics"""
        self.http_requests_total.labels(method=method, endpoint=endpoint, status=str(status)).inc()
        self.http_request_duration.labels(method=method, endpoint=endpoint).observe(duration)
    
    def record_tool_call(self, tool_name: str, status: str, duration: float):
        """Record tool call metrics"""
        self.tool_calls_total.labels(tool_name=tool_name, status=status).inc()
        self.tool_call_duration.labels(tool_name=tool_name).observe(duration)
    
    def record_memory_search(self, search_type: str, duration: float):
        """Record memory search metrics"""
        self.memory_searches.labels(search_type=search_type).inc()
        self.memory_search_duration.labels(search_type=search_type).observe(duration)
    
    def record_workflow_execution(self, workflow_type: str, status: str, duration: float):
        """Record workflow execution metrics"""
        self.langgraph_workflows.labels(workflow_type=workflow_type, status=status).inc()
        self.langgraph_workflow_duration.labels(workflow_type=workflow_type).observe(duration)
    
    def record_agent_execution(self, agent_type: str, status: str, quality_score: float):
        """Record agent execution metrics"""
        self.agent_executions.labels(agent_type=agent_type, status=status).inc()
        self.agent_quality_scores.labels(agent_type=agent_type).observe(quality_score)
    
    def record_input_processing(self, source: str, format: str, status: str, duration: float):
        """Record input processing metrics"""
        self.input_requests.labels(source=source, format=format, status=status).inc()
        self.input_processing_duration.labels(source=source, format=format).observe(duration)
    
    def record_human_task(self, task_type: str, status: str, duration: Optional[float] = None):
        """Record human task metrics"""
        if status == 'created':
            self.human_tasks_created.labels(task_type=task_type).inc()
            self.human_tasks_pending.inc()
        elif status == 'completed':
            self.human_tasks_completed.labels(task_type=task_type).inc()
            self.human_tasks_pending.dec()
            if duration:
                self.human_task_duration.labels(task_type=task_type).observe(duration)
    
    def record_error_compaction(self, context_size: int):
        """Record error compaction metrics"""
        self.error_compactions.inc()
        self.error_context_size.observe(context_size)
    
    def record_state_reduction(self, action_type: str):
        """Record state reduction metrics"""
        self.state_reductions.labels(action_type=action_type).inc()
    
    def update_active_workflows(self, count: int):
        """Update active workflows gauge"""
        self.active_workflows.set(count)
    
    def update_queue_depth(self, queue_name: str, depth: int):
        """Update queue depth gauge"""
        self.queue_depth.labels(queue_name=queue_name).set(depth)
    
    def get_metrics(self) -> str:
        """Get all metrics in Prometheus format"""
        return generate_latest()

# Global metrics collector instance
metrics_collector = MetricsCollector()

def monitor_execution_time(metric_name: str):
    """Decorator to monitor execution time of functions"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record based on metric name
                if hasattr(metrics_collector, metric_name):
                    metric = getattr(metrics_collector, metric_name)
                    if hasattr(metric, 'observe'):
                        metric.observe(duration)
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Function {func.__name__} failed after {duration:.2f}s: {e}")
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record based on metric name
                if hasattr(metrics_collector, metric_name):
                    metric = getattr(metrics_collector, metric_name)
                    if hasattr(metric, 'observe'):
                        metric.observe(duration)
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Function {func.__name__} failed after {duration:.2f}s: {e}")
                raise
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class MonitoringService:
    """
    Monitoring service for comprehensive system observability
    Integrates with Prometheus and Grafana for visualization
    """
    
    def __init__(self):
        self.collector = metrics_collector
        self.service_id = "monitoring_service"
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health metrics"""
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "service_status": "healthy",
            "metrics_collected": True,
            "prometheus_endpoint": "/metrics",
            "grafana_dashboard": "/grafana/d/ai-agent-overview",
            "alert_rules_active": True
        }
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all collected metrics"""
        return {
            "http_requests": "Total HTTP requests processed",
            "tool_calls": "AI tool calls and executions", 
            "memory_operations": "Memory search and consolidation operations",
            "workflow_executions": "LangGraph workflow executions",
            "agent_coordinations": "Multi-agent orchestrations",
            "input_processing": "Universal input handler operations",
            "human_tasks": "Human-in-the-loop task management",
            "state_management": "Stateless reducer operations",
            "vector_database": "PGVector operations and performance",
            "business_metrics": "RFQ workflow and business logic metrics"
        }
    
    def export_metrics(self) -> str:
        """Export metrics in Prometheus format"""
        return self.collector.get_metrics()
    
    def get_monitoring_capabilities(self) -> Dict[str, Any]:
        """Return monitoring service capabilities"""
        return {
            "service_id": self.service_id,
            "monitoring_type": "prometheus_grafana",
            "capabilities": [
                "real_time_metrics_collection",
                "performance_monitoring",
                "error_tracking",
                "business_metrics",
                "alerting_rules",
                "dashboard_visualization"
            ],
            "metrics_categories": [
                "system_health",
                "performance_metrics", 
                "business_logic",
                "error_management",
                "resource_utilization"
            ],
            "factor_coverage": "All 12 factors monitored",
            "description": "Comprehensive monitoring for LangGraph AI Agent system",
            "version": "1.0.0",
            "endpoints": {
                "metrics": "/metrics",
                "health": "/api/monitoring/health",
                "summary": "/api/monitoring/summary"
            }
        }