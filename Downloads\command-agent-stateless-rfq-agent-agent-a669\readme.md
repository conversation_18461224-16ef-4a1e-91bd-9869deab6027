# LangGraph AI Agent System - Complete Deployment Guide

## 🚀 System Overview

This is a comprehensive refactoring of the original Langbase AI agent system to LangGraph with enterprise-grade features implementing all 12-Factor Agent principles. The system provides:

- **LangGraph-based stateful workflow orchestration**
- **PostgreSQL 18 + pgvector semantic memory system**
- **Perplexity API integration for enhanced AI capabilities**
- **Complete Docker containerization with monitoring**
- **Production-ready deployment with scaling capabilities**

## 📋 Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows 10/11
- **RAM**: Minimum 8GB, Recommended 16GB
- **Storage**: Minimum 20GB free space
- **Network**: Internet connection for API access

### Software Dependencies
- **Docker**: Version 20.10+ 
- **Docker Compose**: Version 2.0+
- **Git**: For version control
- **Node.js**: 18+ (for frontend development)

### API Keys Required
- **Perplexity API Key**: Sign up at https://www.perplexity.ai/

## 🛠️ Installation & Deployment

### Step 1: Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd command-agent-stateless-rfq-agent-agent-a669

# Copy environment template
cp .env.production .env

# Edit environment file with your API keys
nano .env  # or use your preferred editor
```

### Step 2: Configure Environment

Edit `.env` file and set the required values:

```bash
# Required: Add your Perplexity API key
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Optional: Customize other settings
ENVIRONMENT=production
LOG_LEVEL=INFO
MAX_CONCURRENT_WORKFLOWS=50
CONTEXT_WINDOW_SIZE=32000
```

### Step 3: Deploy the System

**For Linux/macOS:**
```bash
# Make deployment script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh deploy
```

**For Windows:**
```powershell
# Run PowerShell deployment script
.\deploy.ps1 deploy
```

### Step 4: Verify Deployment

The deployment script will automatically:
1. ✅ Check prerequisites
2. ✅ Create required directories
3. ✅ Generate secure passwords
4. ✅ Build Docker images
5. ✅ Deploy all services
6. ✅ Run database migrations
7. ✅ Validate deployment

**Expected Output:**
```
==========================================
         AI AGENT SYSTEM DEPLOYED
==========================================

Services:
  • Main API: http://localhost/api
  • Agent Service: http://localhost:8000
  • Memory Service: http://localhost:8003
  • Grafana Dashboard: http://localhost:3000
  • Prometheus Metrics: http://localhost:9090
  • RabbitMQ Management: http://localhost:15672
==========================================
```

## 🏗️ Architecture Overview

### 12-Factor Agent Principles Implementation

| Factor | Implementation | Status |
|--------|---------------|---------|
| **Factor 1**: Natural Language to Tool Calls | Perplexity API with structured outputs | ✅ Complete |
| **Factor 2**: Own Your Prompts | Version-controlled prompt management in PostgreSQL | ✅ Complete |
| **Factor 3**: Own Your Context Window | pgvector semantic memory with intelligent retrieval | ✅ Complete |
| **Factor 4**: Tools Are Just Structured Outputs | Tool registry with JSON schemas and validation | ✅ Complete |
| **Factor 5**: Unify Execution State and Business State | PostgreSQL state management with Redis caching | ✅ Complete |
| **Factor 6**: Launch/Pause/Resume | Checkpoint system with workflow control APIs | ✅ Complete |
| **Factor 7**: Contact Humans with Tool Calls | Human-in-the-loop approval workflows | ✅ Complete |
| **Factor 8**: Own Your Control Flow | LangGraph state machine with deterministic flow | ✅ Complete |
| **Factor 9**: Compact Errors into Context Window | Error summarization and pattern learning | ✅ Complete |
| **Factor 10**: Small, Focused Agents | Modular agent architecture with clear boundaries | ✅ Complete |
| **Factor 11**: Trigger from Anywhere | Multi-channel input handling via REST APIs | ✅ Complete |
| **Factor 12**: Stateless Reducer | Pure function workflow with immutable state | ✅ Complete |

### Service Architecture

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Nginx]
    end
    
    subgraph "Application Services"
        AS[Agent Service]
        LO[LangGraph Orchestrator]
        MS[Memory Service]
        CW[Celery Workers]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL + pgvector)]
        RD[(Redis Cache)]
        MQ[RabbitMQ]
    end
    
    subgraph "Monitoring"
        PR[Prometheus]
        GR[Grafana]
        FL[Fluentd]
    end
    
    subgraph "External APIs"
        PA[Perplexity API]
    end
    
    LB --> AS
    LB --> LO
    LB --> MS
    
    AS --> PG
    AS --> RD
    AS --> PA
    
    LO --> PG
    LO --> RD
    
    MS --> PG
    MS --> RD
    
    CW --> MQ
    CW --> PG
    
    PR --> AS
    PR --> LO
    PR --> MS
    
    GR --> PR
    FL --> AS
```

## 🔧 Usage Guide

### Basic RFQ Processing

1. **Access the Web Interface**: Open http://localhost/
2. **Submit RFQ Request**: Enter natural language procurement request
3. **Monitor Progress**: Track workflow execution in real-time
4. **Review Results**: Analyze vendor recommendations and market intelligence
5. **Approve/Reject**: Handle human approval tasks as needed

### API Usage Examples

**Submit RFQ via API:**
```bash
curl -X POST http://localhost/api/rfq/process \
  -H "Content-Type: application/json" \
  -d '{
    "natural_language_request": "I need 50 business laptops for our IT department. Budget around $50,000. Need them urgently for new employee onboarding in Bangalore.",
    "user_id": "user_123",
    "department": "IT",
    "urgency": "high"
  }'
```

**Check Workflow Status:**
```bash
curl http://localhost/api/workflows/{execution_id}/status
```

**Search Memory:**
```bash
curl -X POST http://localhost/api/memory/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "laptop procurement IT department",
    "limit": 10
  }'
```

### Advanced Features

#### Workflow Control
- **Pause Workflow**: `POST /api/workflows/{id}/pause`
- **Resume Workflow**: `POST /api/workflows/{id}/resume`
- **Get Status**: `GET /api/workflows/{id}/status`

#### Human-in-the-Loop
- **Get Pending Tasks**: `GET /api/human-tasks`
- **Respond to Task**: `POST /api/human-tasks/{id}/respond`

#### Memory Management
- **Search Memories**: `POST /api/memory/search`
- **Trigger Consolidation**: `POST /api/memory/consolidate`

## 📊 Monitoring & Observability

### Grafana Dashboards
- **URL**: http://localhost:3000
- **Username**: admin
- **Password**: Check `secrets/grafana_password.txt`

**Available Dashboards:**
- Agent Performance Metrics
- Workflow Execution Analytics
- Memory System Statistics
- Database Performance
- API Response Times

### Prometheus Metrics
- **URL**: http://localhost:9090
- **Metrics Collected**:
  - Workflow execution times
  - Tool call performance
  - Memory search latency
  - Database query performance
  - Error rates and patterns

### Log Aggregation
- **Location**: `./logs/`
- **Structure**:
  ```
  logs/
  ├── agent/          # Agent service logs
  ├── orchestrator/   # LangGraph orchestrator logs
  ├── memory/         # Memory service logs
  ├── nginx/          # Load balancer logs
  ├── postgres/       # Database logs
  └── deployment_*.log # Deployment logs
  ```

## 🛡️ Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- API key management

### Data Protection
- Encrypted secrets management
- Database connection encryption
- HTTPS termination at load balancer

### Security Hardening
- Non-root container execution
- Resource limits and quotas
- Network isolation
- Security scanning enabled

## 🔄 Management Operations

### Daily Operations

**View System Status:**
```bash
# Linux/macOS
./deploy.sh status

# Windows
.\deploy.ps1 status
```

**View Logs:**
```bash
# All services
./deploy.sh logs

# Specific service
./deploy.sh logs agent-service
```

**Backup Data:**
```bash
./deploy.sh backup
```

### Scaling Operations

**Scale Specific Services:**
```bash
# Scale agent service
docker-compose up -d --scale agent-service=3

# Scale celery workers
docker-compose up -d --scale celery-worker=4
```

**Resource Monitoring:**
```bash
# Monitor resource usage
docker stats

# Check container health
docker-compose ps
```

### Maintenance

**Update System:**
```bash
# Pull latest images
docker-compose pull

# Restart with new images
./deploy.sh restart
```

**Clean Up:**
```bash
# Remove unused containers and images
docker system prune -a

# Clean up volumes (⚠️ destroys data)
docker-compose down -v
```

## 🧪 Testing & Validation

### Health Checks
- **Main API**: http://localhost/health
- **Agent Service**: http://localhost:8001/health
- **Memory Service**: http://localhost:8003/health

### Integration Tests
```bash
# Test RFQ workflow end-to-end
curl -X POST http://localhost/api/rfq/process \
  -H "Content-Type: application/json" \
  -d '{"natural_language_request": "Test RFQ for 5 laptops", "user_id": "test_user"}'

# Verify memory search
curl -X POST http://localhost/api/memory/search \
  -H "Content-Type: application/json" \
  -d '{"query": "test", "limit": 5}'
```

## 🐛 Troubleshooting

### Common Issues

**Port Conflicts:**
```bash
# Check port usage
netstat -tlnp | grep :8000

# Kill conflicting processes
sudo lsof -ti:8000 | xargs kill -9
```

**Database Connection Issues:**
```bash
# Check PostgreSQL status
docker-compose logs postgres

# Restart database
docker-compose restart postgres
```

**Memory Issues:**
```bash
# Check memory usage
docker stats

# Increase Docker memory limits
# Edit Docker Desktop settings or daemon.json
```

### Log Analysis
```bash
# Check for errors in logs
grep -i error logs/agent/*.log

# Monitor live logs
tail -f logs/deployment_*.log
```

### Performance Tuning
```bash
# Monitor performance metrics
curl http://localhost:9090/api/v1/query?query=up

# Check database performance
docker-compose exec postgres psql -U agent_user -d agent_db -c "SELECT * FROM pg_stat_activity;"
```

## 📚 Development Guide

### Local Development Setup
```bash
# Install Python dependencies
pip install -r requirements/development.txt

# Run tests
pytest tests/

# Code formatting
black services/agent/
isort services/agent/
```

### Adding New Tools
```python
# Register new tool
from services.agent.services.tool_service import ToolService

tool_service = ToolService()
await tool_service.register_tool(
    name="my_new_tool",
    description="Description of the tool",
    schema={"type": "object", "properties": {...}},
    implementation=my_tool_function,
    category="custom"
)
```

### Extending Memory System
```python
# Store custom memories
from services.agent.services.memory_service import MemoryService

memory_service = MemoryService()
await memory_service.store_memory(
    content="Custom memory content",
    memory_type="custom_type",
    metadata={"key": "value"},
    importance_score=0.8
)
```

## 🔄 Migration from Original System

### Key Changes
1. **Langbase → LangGraph**: State machine workflows with checkpointing
2. **Simple Storage → PostgreSQL + pgvector**: Enterprise database with semantic search
3. **Basic API → 12-Factor Architecture**: Production-ready with monitoring
4. **Single Service → Microservices**: Scalable containerized architecture

### Migration Steps
1. Export existing RFQ data (if any)
2. Deploy new system
3. Migrate data using provided scripts
4. Update frontend integration
5. Test thoroughly before production switch

## 📞 Support & Maintenance

### Support Channels
- **Documentation**: This README and inline code documentation
- **Logs**: Comprehensive logging with structured output
- **Monitoring**: Real-time metrics and alerting
- **Backup**: Automated backup and recovery procedures

### Maintenance Schedule
- **Daily**: Monitor system health and logs
- **Weekly**: Review performance metrics and scaling needs
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Comprehensive system review and optimization

---

## 🎯 Next Steps

1. **Deploy the system** using the provided scripts
2. **Configure monitoring** and set up alerting
3. **Test thoroughly** with sample RFQ requests
4. **Scale as needed** based on usage patterns
5. **Customize** tools and workflows for your specific needs

## 🏆 Success Metrics

The system successfully implements:
- ✅ **100% of 12-Factor Agent principles**
- ✅ **Enterprise-grade containerization**
- ✅ **Comprehensive monitoring and observability**
- ✅ **Production-ready deployment automation**
- ✅ **Semantic memory with pgvector**
- ✅ **Human-in-the-loop workflows**
- ✅ **Real-time workflow control**
- ✅ **Scalable microservices architecture**

**Ready for production deployment! 🚀**