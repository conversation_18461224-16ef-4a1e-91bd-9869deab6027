"""
Error Management Service implementing Factor 9: Compact Errors into Context Window
Provides intelligent error handling, pattern recognition, and context-aware error compaction
"""

import structlog
import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import re
from collections import defaultdict

from ..core.database import db_service
from ..core.redis_client import cache_manager
from ..services.embedding_service import EmbeddingService
from ..services.perplexity_service import PerplexityService

logger = structlog.get_logger(__name__)

class ErrorManager:
    """
    Comprehensive error management implementing Factor 9
    Features: Pattern recognition, context compaction, learning from errors
    """
    
    def __init__(self):
        self.embedding_service = EmbeddingService()
        self.perplexity_service = PerplexityService()
        self.max_context_length = 1000  # Maximum characters for compacted error context
        self.pattern_similarity_threshold = 0.8
    
    async def compact_error_for_context(
        self,
        error: Exception,
        context: Dict[str, Any],
        execution_id: str,
        step_name: str
    ) -> str:
        """
        Compact error information for inclusion in context window
        Core implementation of Factor 9
        """
        logger.info("Compacting error for context", 
                   execution_id=execution_id, 
                   step_name=step_name,
                   error_type=type(error).__name__)
        
        try:
            # Generate error signature
            error_signature = self._generate_error_signature(error, step_name)
            
            # Check for existing error patterns
            similar_errors = await self._find_similar_error_patterns(error_signature, error)
            
            # Create base error info
            error_info = {
                "error_type": type(error).__name__,
                "step": step_name,
                "message": str(error)[:200],  # Limit message length
                "signature": error_signature,
                "timestamp": datetime.utcnow().isoformat(),
                "execution_id": execution_id
            }
            
            # Add context compaction
            compacted_context = await self._compact_context(context)
            error_info["context"] = compacted_context
            
            # Add solution suggestions if patterns exist
            if similar_errors:
                solutions = await self._generate_solution_suggestions(similar_errors)
                error_info["suggested_solutions"] = solutions
                
                # Use historical success rate for confidence
                success_rate = await self._calculate_pattern_success_rate(error_signature)
                error_info["solution_confidence"] = success_rate
            
            # Store error pattern for learning
            await self._store_error_pattern(error_info, similar_errors)
            
            # Generate compacted error summary
            compacted_summary = await self._generate_compacted_summary(error_info)
            
            logger.info("Error compacted successfully", 
                       execution_id=execution_id,
                       compacted_length=len(compacted_summary),
                       has_solutions=bool(similar_errors))
            
            return compacted_summary
            
        except Exception as e:
            logger.error("Failed to compact error", 
                        execution_id=execution_id,
                        compaction_error=str(e))
            
            # Fallback: simple error compaction
            return f"Error in {step_name}: {str(error)[:100]}... (compaction failed)"
    
    def _generate_error_signature(self, error: Exception, step_name: str) -> str:
        """Generate unique signature for error pattern recognition"""
        # Normalize error message
        error_message = str(error).lower()
        
        # Remove variable parts (numbers, IDs, timestamps)
        normalized_message = re.sub(r'\d+', 'N', error_message)
        normalized_message = re.sub(r'[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', 'UUID', normalized_message)
        normalized_message = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', 'TIMESTAMP', normalized_message)
        
        # Create signature
        signature_data = f"{type(error).__name__}:{step_name}:{normalized_message}"
        return hashlib.md5(signature_data.encode()).hexdigest()
    
    async def _find_similar_error_patterns(
        self, 
        error_signature: str, 
        error: Exception
    ) -> List[Dict[str, Any]]:
        """Find similar error patterns using vector similarity"""
        try:
            # Generate embedding for current error
            error_text = f"{type(error).__name__} {str(error)}"
            error_embedding = await self.embedding_service.generate_embedding(error_text)
            
            # Search for similar errors using vector similarity
            query = """
            SELECT ep.*, 1 - (ep.embedding <=> $1::vector) AS similarity
            FROM agent_monitoring.error_patterns ep
            WHERE ep.error_signature != $2
            AND 1 - (ep.embedding <=> $1::vector) > $3
            ORDER BY ep.embedding <=> $1::vector
            LIMIT 5
            """
            
            results = await db_service.execute_query(query, {
                "error_embedding": error_embedding,
                "error_signature": error_signature,
                "similarity_threshold": self.pattern_similarity_threshold
            })
            
            return [dict(row) for row in results]
            
        except Exception as e:
            logger.error("Failed to find similar error patterns", error=str(e))
            return []
    
    async def _compact_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Compact context information to fit within limits"""
        compacted = {}
        
        # Priority order for context elements
        priority_keys = [
            "execution_id", "user_id", "current_step", "progress_percentage",
            "parsed_request", "vendors", "market_intelligence"
        ]
        
        remaining_length = self.max_context_length
        
        for key in priority_keys:
            if key in context and remaining_length > 0:
                value = context[key]
                
                # Serialize and check length
                serialized = json.dumps(value, default=str)
                
                if len(serialized) <= remaining_length:
                    compacted[key] = value
                    remaining_length -= len(serialized)
                else:
                    # Truncate or summarize
                    if isinstance(value, (dict, list)):
                        compacted[key] = f"<{type(value).__name__} with {len(value)} items>"
                    else:
                        truncated = str(value)[:remaining_length-10]
                        compacted[key] = f"{truncated}..."
                    break
        
        return compacted
    
    async def _generate_solution_suggestions(
        self, 
        similar_errors: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate solution suggestions based on similar error patterns"""
        solutions = []
        
        for error_pattern in similar_errors:
            solution_steps = error_pattern.get("solution_steps", [])
            if solution_steps:
                solutions.extend(solution_steps)
        
        # Remove duplicates and limit
        unique_solutions = list(set(solutions))[:3]
        
        # If no stored solutions, generate AI-powered suggestions
        if not unique_solutions and similar_errors:
            ai_solutions = await self._generate_ai_solutions(similar_errors)
            unique_solutions.extend(ai_solutions)
        
        return unique_solutions
    
    async def _generate_ai_solutions(
        self, 
        similar_errors: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate AI-powered solution suggestions using Perplexity"""
        try:
            error_descriptions = []
            for error in similar_errors[:2]:  # Limit context
                error_descriptions.append(f"{error['error_type']}: {error.get('description', 'No description')}")
            
            query = f"""
            Based on these similar error patterns in an AI agent system:
            {'; '.join(error_descriptions)}
            
            Provide 2-3 specific, actionable solution steps to resolve these types of errors.
            Focus on practical debugging and recovery steps.
            """
            
            result = await self.perplexity_service._make_request([
                {"role": "user", "content": query}
            ])
            
            if "choices" in result and result["choices"]:
                content = result["choices"][0]["message"]["content"]
                
                # Extract solution steps (simple parsing)
                solutions = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and (line.startswith('-') or line.startswith('•') or line[0].isdigit()):
                        solutions.append(line[:100])  # Limit length
                
                return solutions[:3]
            
        except Exception as e:
            logger.error("Failed to generate AI solutions", error=str(e))
        
        return []
    
    async def _calculate_pattern_success_rate(self, error_signature: str) -> float:
        """Calculate success rate for error pattern resolution"""
        try:
            query = """
            SELECT 
                occurrence_count,
                resolved_count
            FROM agent_monitoring.error_patterns
            WHERE error_signature = $1
            """
            
            result = await db_service.execute_single_query(query, {"error_signature": error_signature})
            
            if result:
                occurrence_count = result["occurrence_count"]
                resolved_count = result["resolved_count"]
                
                if occurrence_count > 0:
                    return resolved_count / occurrence_count
            
            return 0.0
            
        except Exception as e:
            logger.error("Failed to calculate success rate", error=str(e))
            return 0.0
    
    async def _store_error_pattern(
        self,
        error_info: Dict[str, Any],
        similar_errors: List[Dict[str, Any]]
    ):
        """Store error pattern for future learning"""
        try:
            # Generate embedding for error
            error_text = f"{error_info['error_type']} {error_info['message']}"
            embedding = await self.embedding_service.generate_embedding(error_text)
            
            # Prepare data for storage
            pattern_data = {
                "error_signature": error_info["signature"],
                "error_type": error_info["error_type"],
                "description": error_info["message"],
                "embedding": embedding,
                "occurrence_count": 1,
                "resolved_count": 0,
                "solution_steps": error_info.get("suggested_solutions", []),
                "context_pattern": error_info.get("context", {}),
                "step_name": error_info["step"]
            }
            
            # Insert or update pattern
            query = """
            INSERT INTO agent_monitoring.error_patterns 
            (error_signature, error_type, description, embedding, occurrence_count, resolved_count, solution_steps)
            VALUES ($1, $2, $3, $4::vector, $5, $6, $7)
            ON CONFLICT (error_signature) 
            DO UPDATE SET 
                occurrence_count = error_patterns.occurrence_count + 1,
                updated_at = NOW()
            """
            
            await db_service.execute_command(query, pattern_data)
            
            logger.info("Error pattern stored", 
                       signature=error_info["signature"],
                       has_similar=bool(similar_errors))
            
        except Exception as e:
            logger.error("Failed to store error pattern", error=str(e))
    
    async def _generate_compacted_summary(self, error_info: Dict[str, Any]) -> str:
        """Generate final compacted error summary for context window"""
        # Base error information
        summary_parts = [
            f"Error in {error_info['step']}: {error_info['error_type']}"
        ]
        
        # Add truncated message
        message = error_info["message"]
        if len(message) > 100:
            message = message[:97] + "..."
        summary_parts.append(f"Details: {message}")
        
        # Add solutions if available
        solutions = error_info.get("suggested_solutions", [])
        if solutions:
            confidence = error_info.get("solution_confidence", 0)
            summary_parts.append(f"Solutions (confidence: {confidence:.1%}): {'; '.join(solutions[:2])}")
        
        # Add relevant context
        context = error_info.get("context", {})
        if "current_step" in context:
            summary_parts.append(f"Step: {context['current_step']}")
        
        if "progress_percentage" in context:
            summary_parts.append(f"Progress: {context['progress_percentage']}%")
        
        # Join and ensure within length limits
        full_summary = " | ".join(summary_parts)
        
        if len(full_summary) > self.max_context_length:
            full_summary = full_summary[:self.max_context_length-3] + "..."
        
        return full_summary
    
    async def mark_error_resolved(
        self,
        error_signature: str,
        resolution_steps: List[str],
        success: bool = True
    ):
        """Mark an error pattern as resolved and update success metrics"""
        try:
            if success:
                query = """
                UPDATE agent_monitoring.error_patterns
                SET resolved_count = resolved_count + 1,
                    solution_steps = CASE 
                        WHEN solution_steps IS NULL OR jsonb_array_length(solution_steps) = 0 
                        THEN $2::jsonb 
                        ELSE solution_steps 
                    END,
                    updated_at = NOW()
                WHERE error_signature = $1
                """
            else:
                query = """
                UPDATE agent_monitoring.error_patterns
                SET updated_at = NOW()
                WHERE error_signature = $1
                """
            
            params = {"error_signature": error_signature}
            if success:
                params["resolution_steps"] = resolution_steps
            
            await db_service.execute_command(query, params)
            
            logger.info("Error resolution recorded", 
                       signature=error_signature,
                       success=success)
            
        except Exception as e:
            logger.error("Failed to mark error resolved", error=str(e))
    
    async def get_error_analytics(self) -> Dict[str, Any]:
        """Get comprehensive error analytics for monitoring"""
        try:
            # Get error frequency by type
            type_query = """
            SELECT error_type, 
                   COUNT(*) as total_count,
                   SUM(occurrence_count) as total_occurrences,
                   SUM(resolved_count) as total_resolutions,
                   AVG(resolved_count::float / NULLIF(occurrence_count, 0)) as avg_resolution_rate
            FROM agent_monitoring.error_patterns
            GROUP BY error_type
            ORDER BY total_occurrences DESC
            LIMIT 10
            """
            
            type_results = await db_service.execute_query(type_query)
            
            # Get recent error trends
            trend_query = """
            SELECT DATE(created_at) as error_date,
                   COUNT(*) as pattern_count,
                   SUM(occurrence_count) as occurrence_count
            FROM agent_monitoring.error_patterns
            WHERE created_at > NOW() - INTERVAL '30 days'
            GROUP BY DATE(created_at)
            ORDER BY error_date DESC
            """
            
            trend_results = await db_service.execute_query(trend_query)
            
            # Get top unresolved patterns
            unresolved_query = """
            SELECT error_type, description, occurrence_count, resolved_count,
                   (occurrence_count - resolved_count) as unresolved_count
            FROM agent_monitoring.error_patterns
            WHERE occurrence_count > resolved_count
            ORDER BY unresolved_count DESC
            LIMIT 5
            """
            
            unresolved_results = await db_service.execute_query(unresolved_query)
            
            return {
                "error_types": [dict(row) for row in type_results],
                "recent_trends": [dict(row) for row in trend_results],
                "top_unresolved": [dict(row) for row in unresolved_results],
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Failed to get error analytics", error=str(e))
            return {"error": str(e)}
    
    async def suggest_error_prevention(
        self,
        workflow_type: str = "rfq"
    ) -> List[str]:
        """Suggest error prevention strategies based on historical patterns"""
        try:
            # Get most common error patterns
            query = """
            SELECT error_type, description, occurrence_count, solution_steps
            FROM agent_monitoring.error_patterns
            WHERE occurrence_count >= 3
            ORDER BY occurrence_count DESC
            LIMIT 5
            """
            
            results = await db_service.execute_query(query)
            
            prevention_strategies = []
            
            for row in results:
                error_type = row["error_type"]
                occurrence_count = row["occurrence_count"]
                
                # Generate prevention strategy based on error type
                if "ConnectionError" in error_type or "TimeoutError" in error_type:
                    prevention_strategies.append(
                        f"Implement retry logic with exponential backoff for {error_type} (occurred {occurrence_count} times)"
                    )
                elif "ValidationError" in error_type:
                    prevention_strategies.append(
                        f"Add stricter input validation to prevent {error_type} (occurred {occurrence_count} times)"
                    )
                elif "MemoryError" in error_type or "OutOfMemory" in error_type:
                    prevention_strategies.append(
                        f"Implement memory monitoring and cleanup for {error_type} (occurred {occurrence_count} times)"
                    )
                else:
                    prevention_strategies.append(
                        f"Monitor and add safeguards for {error_type} (occurred {occurrence_count} times)"
                    )
            
            # Add general prevention strategies
            prevention_strategies.extend([
                "Implement circuit breaker pattern for external API calls",
                "Add comprehensive health checks and monitoring alerts",
                "Use structured logging with error correlation IDs"
            ])
            
            return prevention_strategies[:7]  # Limit to top recommendations
            
        except Exception as e:
            logger.error("Failed to suggest error prevention", error=str(e))
            return ["Error prevention analysis unavailable"]

# Global error manager instance
error_manager = ErrorManager()