"""
Factor 12: Functional Workflow Service
Demonstrates stateless reducer pattern in practice with RFQ workflows
"""

from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import logging

from .stateless_reducer import StatelessAgentService, AgentState, ActionType
from .perplexity_service import PerplexityService
from .memory_service import MemoryService
from ..agents.multi_agent_orchestrator import MultiAgentOrchestrator

logger = logging.getLogger(__name__)

class FunctionalWorkflowService:
    """
    Factor 12: Functional Workflow Service
    
    Demonstrates how to build workflows using stateless reducer patterns:
    - Pure functional workflow steps
    - Immutable state management
    - Composable workflow building
    - Side effect isolation
    """
    
    def __init__(self):
        self.stateless_service = StatelessAgentService()
        self.perplexity = PerplexityService()
        self.memory = MemoryService()
        self.orchestrator = MultiAgentOrchestrator()
        self.service_id = "functional_workflow_service"
    
    # ========== PURE WORKFLOW STEP FUNCTIONS ==========
    
    async def parse_rfq_input_step(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Pure function: Parse RFQ input data"""
        rfq_text = input_data.get("rfq_request", "")
        
        # Use AI to parse but structure as pure function
        try:
            parsed_data = {
                "category": self._extract_category(rfq_text),
                "region": self._extract_region(rfq_text),
                "urgency": self._extract_urgency(rfq_text),
                "budget_range": self._extract_budget(rfq_text),
                "department": input_data.get("department", "procurement"),
                "parsed_at": datetime.utcnow().isoformat()
            }
            
            return {
                "success": True,
                "parsed_data": parsed_data,
                "original_input": rfq_text
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "original_input": rfq_text
            }
    
    async def market_research_step(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Pure function: Execute market research"""
        try:
            parsed_data = input_data.get("previous_result", {}).get("parsed_data", {})
            
            # Call orchestrator but return structured result
            research_result = await self.orchestrator.process_rfq_request(
                rfq_request=input_data.get("rfq_request", ""),
                region=parsed_data.get("region", "Global"),
                category=parsed_data.get("category", "General"),
                budget_range=parsed_data.get("budget_range"),
                urgency=parsed_data.get("urgency", "medium")
            )
            
            return {
                "success": research_result.get("success", False),
                "market_research": research_result.get("market_research"),
                "vendor_discovery": research_result.get("vendor_discovery"),
                "document_generation": research_result.get("document_generation"),
                "execution_time": research_result.get("execution_time", 0),
                "workflow_id": research_result.get("workflow_id")
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def vendor_notification_step(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Pure function: Handle vendor notifications"""
        try:
            market_result = input_data.get("previous_result", {})
            vendor_data = market_result.get("vendor_discovery", {})
            
            vendors_contacted = []
            if vendor_data.get("success"):
                vendor_count = vendor_data.get("total_vendors", 0)
                vendors_contacted = [
                    {
                        "vendor_id": f"vendor_{i}",
                        "notification_sent": True,
                        "sent_at": datetime.utcnow().isoformat()
                    }
                    for i in range(vendor_count)
                ]
            
            return {
                "success": True,
                "vendors_contacted": vendors_contacted,
                "total_notifications": len(vendors_contacted),
                "notification_time": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def workflow_completion_step(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Pure function: Finalize workflow"""
        try:
            # Gather all previous results
            previous_results = input_data.get("previous_result", {})
            
            completion_summary = {
                "workflow_completed": True,
                "completion_time": datetime.utcnow().isoformat(),
                "steps_completed": ["parse_input", "market_research", "vendor_notification"],
                "overall_success": previous_results.get("success", False),
                "summary": {
                    "parsing_successful": bool(input_data.get("parsed_data")),
                    "research_completed": bool(previous_results.get("market_research")),
                    "vendors_notified": previous_results.get("total_notifications", 0)
                }
            }
            
            return completion_summary
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    # ========== HELPER FUNCTIONS (Pure) ==========
    
    def _extract_category(self, text: str) -> str:
        """Extract category from text - pure function"""
        text_lower = text.lower()
        if any(word in text_lower for word in ["laptop", "computer", "it", "software"]):
            return "IT"
        elif any(word in text_lower for word in ["furniture", "desk", "chair"]):
            return "Furniture"
        elif any(word in text_lower for word in ["construction", "building"]):
            return "Construction"
        else:
            return "General"
    
    def _extract_region(self, text: str) -> str:
        """Extract region from text - pure function"""
        text_lower = text.lower()
        if any(word in text_lower for word in ["india", "mumbai", "delhi", "bangalore"]):
            return "India"
        elif any(word in text_lower for word in ["us", "usa", "america", "united states"]):
            return "United States"
        elif any(word in text_lower for word in ["uk", "britain", "england"]):
            return "United Kingdom"
        else:
            return "Global"
    
    def _extract_urgency(self, text: str) -> str:
        """Extract urgency from text - pure function"""
        text_lower = text.lower()
        if any(word in text_lower for word in ["urgent", "asap", "immediately", "rush"]):
            return "urgent"
        elif any(word in text_lower for word in ["high", "priority", "soon"]):
            return "high"
        elif any(word in text_lower for word in ["low", "whenever", "no rush"]):
            return "low"
        else:
            return "medium"
    
    def _extract_budget(self, text: str) -> Optional[str]:
        """Extract budget from text - pure function"""
        import re
        budget_pattern = r'budget[:\s]*\$?([0-9,]+(?:\.[0-9]{2})?)'
        match = re.search(budget_pattern, text.lower())
        return f"${match.group(1)}" if match else None
    
    # ========== WORKFLOW ORCHESTRATION ==========
    
    async def execute_rfq_workflow(self, rfq_request: str, user_id: str = "system") -> Dict[str, Any]:
        """
        Execute complete RFQ workflow using functional patterns
        Demonstrates Factor 12 implementation
        """
        try:
            # Create initial state
            execution_id = f"rfq_workflow_{datetime.utcnow().timestamp()}"
            initial_state = self.stateless_service.create_initial_state(
                execution_id=execution_id,
                context={
                    "rfq_request": rfq_request,
                    "user_id": user_id,
                    "workflow_type": "rfq_processing"
                }
            )
            
            logger.info(f"Starting functional RFQ workflow: {execution_id}")
            
            # Compose workflow using functional composition
            composed_workflow = self.stateless_service.compose_workflow_steps(
                self.parse_rfq_input_step,
                self.market_research_step,
                self.vendor_notification_step,
                self.workflow_completion_step
            )
            
            # Execute composed workflow
            workflow_input = {
                "rfq_request": rfq_request,
                "user_id": user_id,
                "execution_id": execution_id
            }
            
            final_state = await composed_workflow(initial_state, workflow_input)
            
            # Build response from final state
            response = {
                "success": not self.stateless_service.has_errors(final_state),
                "execution_id": execution_id,
                "final_status": self.stateless_service.get_status(final_state),
                "progress": self.stateless_service.get_progress(final_state),
                "results": final_state.results,
                "errors": final_state.errors if self.stateless_service.has_errors(final_state) else [],
                "workflow_metadata": {
                    "start_time": initial_state.last_updated.isoformat(),
                    "end_time": final_state.last_updated.isoformat(),
                    "steps_completed": len(final_state.results),
                    "memories_created": len(final_state.memories)
                },
                "state_transitions": self._extract_state_history(final_state),
                "service_info": {
                    "service_id": self.service_id,
                    "factor_compliance": ["Factor 12: Make Your Agent a Stateless Reducer"],
                    "functional_guarantees": [
                        "Immutable state management",
                        "Pure function operations",
                        "Predictable state transitions",
                        "No hidden side effects"
                    ]
                }
            }
            
            logger.info(f"Functional workflow completed: {execution_id}, Success: {response['success']}")
            
            return response
            
        except Exception as e:
            logger.error(f"Functional workflow failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "service_id": self.service_id
            }
    
    def _extract_state_history(self, final_state: AgentState) -> List[Dict[str, Any]]:
        """Extract state transition history from memories"""
        state_transitions = []
        for memory in final_state.memories:
            if memory.get('type') == 'workflow_step':
                state_transitions.append({
                    "step": memory.get('content'),
                    "timestamp": memory.get('timestamp'),
                    "metadata": memory.get('metadata', {})
                })
        return state_transitions
    
    # ========== ADVANCED FUNCTIONAL PATTERNS ==========
    
    async def execute_parallel_workflow_branches(self, rfq_request: str) -> Dict[str, Any]:
        """
        Execute multiple workflow branches in parallel using functional patterns
        Demonstrates advanced Factor 12 usage
        """
        try:
            execution_id = f"parallel_workflow_{datetime.utcnow().timestamp()}"
            
            # Create separate states for each branch
            research_state = self.stateless_service.create_initial_state(
                execution_id=f"{execution_id}_research",
                context={"branch": "research", "rfq_request": rfq_request}
            )
            
            vendor_state = self.stateless_service.create_initial_state(
                execution_id=f"{execution_id}_vendor",
                context={"branch": "vendor", "rfq_request": rfq_request}
            )
            
            document_state = self.stateless_service.create_initial_state(
                execution_id=f"{execution_id}_document",
                context={"branch": "document", "rfq_request": rfq_request}
            )
            
            # Execute branches in parallel
            import asyncio
            
            research_task = self._execute_research_branch(research_state, rfq_request)
            vendor_task = self._execute_vendor_branch(vendor_state, rfq_request)
            document_task = self._execute_document_branch(document_state, rfq_request)
            
            # Wait for all branches to complete
            results = await asyncio.gather(
                research_task,
                vendor_task,
                document_task,
                return_exceptions=True
            )
            
            # Merge results using functional composition
            merged_state = self._merge_parallel_results(
                execution_id,
                results,
                [research_state, vendor_state, document_state]
            )
            
            return {
                "success": True,
                "execution_id": execution_id,
                "parallel_results": {
                    "research": results[0] if not isinstance(results[0], Exception) else str(results[0]),
                    "vendor": results[1] if not isinstance(results[1], Exception) else str(results[1]),
                    "document": results[2] if not isinstance(results[2], Exception) else str(results[2])
                },
                "merged_state": self.stateless_service.serialize_state(merged_state),
                "pattern": "parallel_functional_execution"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "execution_id": execution_id
            }
    
    async def _execute_research_branch(self, state: AgentState, rfq_request: str) -> Dict[str, Any]:
        """Execute research branch - pure functional operations"""
        state = self.stateless_service.set_status(state, "running_research")
        
        # Simulate research operation
        research_result = {
            "market_analysis": "Completed market analysis",
            "price_range": "$1000-$5000",
            "suppliers_found": 15,
            "research_quality": 0.9
        }
        
        state = self.stateless_service.add_result(state, "research", research_result)
        state = self.stateless_service.update_progress(state, 1.0)
        state = self.stateless_service.set_status(state, "completed")
        
        return research_result
    
    async def _execute_vendor_branch(self, state: AgentState, rfq_request: str) -> Dict[str, Any]:
        """Execute vendor branch - pure functional operations"""
        state = self.stateless_service.set_status(state, "running_vendor_discovery")
        
        # Simulate vendor discovery
        vendor_result = {
            "vendors_identified": 12,
            "tier_1_vendors": 4,
            "tier_2_vendors": 5,
            "tier_3_vendors": 3,
            "discovery_quality": 0.85
        }
        
        state = self.stateless_service.add_result(state, "vendors", vendor_result)
        state = self.stateless_service.update_progress(state, 1.0)
        state = self.stateless_service.set_status(state, "completed")
        
        return vendor_result
    
    async def _execute_document_branch(self, state: AgentState, rfq_request: str) -> Dict[str, Any]:
        """Execute document generation branch - pure functional operations"""
        state = self.stateless_service.set_status(state, "running_document_generation")
        
        # Simulate document generation
        document_result = {
            "document_id": f"RFQ-{datetime.utcnow().strftime('%Y%m%d')}",
            "sections_generated": 8,
            "compliance_score": 0.95,
            "document_quality": 0.92
        }
        
        state = self.stateless_service.add_result(state, "document", document_result)
        state = self.stateless_service.update_progress(state, 1.0)
        state = self.stateless_service.set_status(state, "completed")
        
        return document_result
    
    def _merge_parallel_results(self, execution_id: str, results: List[Any], 
                              states: List[AgentState]) -> AgentState:
        """Merge parallel execution results - pure function"""
        # Create merged state
        merged_state = self.stateless_service.create_initial_state(
            execution_id=f"{execution_id}_merged",
            context={"merge_type": "parallel_results"}
        )
        
        # Add all results
        for i, result in enumerate(results):
            if not isinstance(result, Exception):
                merged_state = self.stateless_service.add_result(
                    merged_state, f"branch_{i}", result
                )
        
        # Merge memories from all states
        for state in states:
            for memory in state.memories:
                merged_state = self.stateless_service.add_memory(
                    merged_state,
                    f"Branch memory: {memory.get('content')}",
                    "merged_memory",
                    memory.get('metadata', {})
                )
        
        # Set final status and progress
        merged_state = self.stateless_service.update_progress(merged_state, 1.0)
        merged_state = self.stateless_service.set_status(merged_state, "completed")
        
        return merged_state
    
    def get_service_capabilities(self) -> Dict[str, Any]:
        """Return service capabilities"""
        return {
            "service_id": self.service_id,
            "service_type": "functional_workflow",
            "capabilities": [
                "functional_workflow_execution",
                "stateless_state_management",
                "workflow_composition",
                "parallel_execution",
                "pure_function_operations"
            ],
            "workflow_patterns": [
                "sequential_composition",
                "parallel_execution",
                "branch_merging",
                "state_immutability"
            ],
            "description": "Functional workflow service demonstrating stateless reducer patterns",
            "version": "1.0.0",
            "factor_compliance": ["Factor 12: Make Your Agent a Stateless Reducer"],
            "functional_guarantees": [
                "No state mutations",
                "Pure function operations",
                "Predictable workflows",
                "Composable components",
                "Side effect isolation"
            ]
        }