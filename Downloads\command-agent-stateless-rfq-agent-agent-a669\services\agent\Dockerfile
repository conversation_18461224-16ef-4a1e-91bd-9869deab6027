# services/agent/Dockerfile
ARG PYTHON_VERSION=3.11
FROM python:${PYTHON_VERSION}-slim as base

# Build arguments
ARG BUILD_DATE
ARG GIT_COMMIT
ARG PYTHON_VERSION

# Labels for metadata
LABEL maintainer="AI Team <<EMAIL>>" \
      version="1.0.0" \
      description="LangGraph AI Agent Service" \
      build-date=${BUILD_DATE} \
      git-commit=${GIT_COMMIT} \
      python-version=${PYTHON_VERSION}

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    libpq-dev \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# ========== DEPENDENCY STAGE ==========
FROM base as dependencies

# Copy requirements files from the project root
COPY requirements/ ./requirements/

# Install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements/base.txt && \
    pip install -r requirements/production.txt

# ========== DEVELOPMENT STAGE ==========
FROM dependencies as development

# Install development dependencies
COPY requirements/development.txt ./requirements/
RUN pip install -r requirements/development.txt

# Copy source code
COPY . .

# Change ownership to app user
RUN chown -R appuser:appuser /app

USER appuser

# Expose ports
EXPOSE 8000 8001

# Command for development
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# ========== PRODUCTION STAGE ==========
FROM dependencies as production

# Copy only necessary files
COPY --from=dependencies /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=dependencies /usr/local/bin /usr/local/bin

# Copy application code
COPY services/agent/ ./

# Create directories and copy config/scripts
RUN mkdir -p ./config ./scripts
COPY config/ ./config/
COPY scripts/ ./scripts/

# Create required directories
RUN mkdir -p /app/logs /app/checkpoints /app/temp && \
    chown -R appuser:appuser /app

# Security: Use non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Expose ports
EXPOSE 8000 8001

# Production command
CMD ["python", "-m", "gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]