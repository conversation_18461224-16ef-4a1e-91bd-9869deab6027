"""
Factor 10: Small, Focused Agents - Vendor Discovery Agent
Specialized agent for vendor identification and qualification
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json
import logging
from langgraph import StateGraph
from langgraph.graph import END
from enum import Enum

from ..services.perplexity_service import PerplexityService
from ..services.memory_service import MemoryService
from ..services.error_manager import ErrorManager

logger = logging.getLogger(__name__)

class VendorTier(Enum):
    TIER_1 = "tier_1"  # Global/Enterprise
    TIER_2 = "tier_2"  # Regional/Specialized  
    TIER_3 = "tier_3"  # Local/Niche

@dataclass
class VendorProfile:
    vendor_id: str
    name: str
    tier: VendorTier
    contact_info: Dict[str, Any]
    capabilities: List[str]
    regions_served: List[str]
    rating: float
    pricing_model: str
    last_updated: datetime = field(default_factory=datetime.utcnow)

@dataclass
class VendorDiscoveryState:
    category: str
    region: str
    budget_range: Optional[str] = None
    
    # Results
    tier_1_vendors: List[VendorProfile] = field(default_factory=list)
    tier_2_vendors: List[VendorProfile] = field(default_factory=list)
    tier_3_vendors: List[VendorProfile] = field(default_factory=list)
    vendor_recommendations: List[Dict[str, Any]] = field(default_factory=list)
    total_vendors_found: int = 0
    discovery_quality_score: float = 0.0

class VendorDiscoveryAgent:
    """Factor 10: Small, Focused Agent for Vendor Discovery"""
    
    def __init__(self):
        self.perplexity = PerplexityService()
        self.memory = MemoryService()
        self.error_manager = ErrorManager()
        self.agent_id = "vendor_discovery_agent"
        self.workflow = self._build_workflow()
    
    def _build_workflow(self):
        """Build vendor discovery workflow"""
        workflow = StateGraph(VendorDiscoveryState)
        
        workflow.add_node("discover_vendors", self._discover_all_vendors)
        workflow.add_node("generate_recommendations", self._generate_recommendations)
        
        workflow.set_entry_point("discover_vendors")
        workflow.add_edge("discover_vendors", "generate_recommendations")
        workflow.add_edge("generate_recommendations", END)
        
        return workflow.compile()
    
    async def _discover_all_vendors(self, state: VendorDiscoveryState) -> VendorDiscoveryState:
        """Discover vendors across all tiers"""
        try:
            # Single comprehensive vendor discovery query
            discovery_query = f"""
            Identify comprehensive vendor landscape for {state.category} in {state.region}.
            
            Categorize vendors into three tiers:
            
            TIER 1 (Global/Enterprise):
            - Fortune 500 or globally recognized companies
            - Market leaders with proven track record
            - Global delivery capabilities
            
            TIER 2 (Regional/Specialized):
            - Regional market leaders
            - Specialized vendors with niche expertise
            - Mid-market companies with strong regional presence
            
            TIER 3 (Local/Niche):
            - Local companies with community presence
            - Boutique specialists with unique capabilities
            - Emerging vendors with innovative approaches
            
            For each vendor provide:
            - Company name and tier classification
            - Core capabilities and specializations
            - Geographic coverage
            - Market position and reputation
            - Contact information for procurement
            - Competitive advantages
            """
            
            vendor_research = await self.perplexity.research_with_sources(
                query=discovery_query,
                context="comprehensive_vendor_discovery"
            )
            
            # Parse vendors by tier
            all_vendors = self._parse_vendor_data(vendor_research["content"])
            
            # Categorize by tier
            state.tier_1_vendors = [v for v in all_vendors if v.tier == VendorTier.TIER_1]
            state.tier_2_vendors = [v for v in all_vendors if v.tier == VendorTier.TIER_2]
            state.tier_3_vendors = [v for v in all_vendors if v.tier == VendorTier.TIER_3]
            state.total_vendors_found = len(all_vendors)
            
            # Store vendor intelligence
            await self.memory.store_conversation_memory(
                agent_id=self.agent_id,
                content=f"Vendor discovery results: {json.dumps([v.__dict__ for v in all_vendors], default=str)}",
                memory_type="vendor_intelligence",
                metadata={"category": state.category, "region": state.region}
            )
            
            logger.info(f"Discovered {len(all_vendors)} vendors: T1={len(state.tier_1_vendors)}, T2={len(state.tier_2_vendors)}, T3={len(state.tier_3_vendors)}")
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=f"vendor_discovery_{datetime.utcnow().timestamp()}",
                step_name="discover_vendors"
            )
            logger.error(f"Vendor discovery failed: {error_context}")
            return state
    
    async def _generate_recommendations(self, state: VendorDiscoveryState) -> VendorDiscoveryState:
        """Generate vendor recommendations"""
        try:
            all_vendors = state.tier_1_vendors + state.tier_2_vendors + state.tier_3_vendors
            
            # Score and rank vendors
            scored_vendors = []
            for vendor in all_vendors:
                score = self._score_vendor(vendor, state)
                scored_vendors.append({
                    "vendor": vendor,
                    "match_score": score,
                    "recommendation_reason": self._get_recommendation_reason(vendor, score),
                    "tier": vendor.tier.value
                })
            
            # Sort by score and take top recommendations
            scored_vendors.sort(key=lambda x: x["match_score"], reverse=True)
            state.vendor_recommendations = scored_vendors[:10]
            
            # Calculate quality score
            state.discovery_quality_score = min(1.0, len(all_vendors) / 15) * 0.8 + 0.2
            
            logger.info(f"Generated {len(state.vendor_recommendations)} recommendations with quality score: {state.discovery_quality_score}")
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=f"vendor_discovery_{datetime.utcnow().timestamp()}",
                step_name="generate_recommendations"
            )
            logger.error(f"Recommendation generation failed: {error_context}")
            return state
    
    def _parse_vendor_data(self, content: str) -> List[VendorProfile]:
        """Parse vendor data from research content"""
        vendors = []
        
        # Mock vendor data - in real implementation would parse from content
        vendor_templates = [
            {"name": "GlobalTech Solutions", "tier": VendorTier.TIER_1, "rating": 4.8},
            {"name": "Regional IT Partners", "tier": VendorTier.TIER_2, "rating": 4.5},
            {"name": "Local Tech Specialists", "tier": VendorTier.TIER_3, "rating": 4.2},
            {"name": "Enterprise Systems Corp", "tier": VendorTier.TIER_1, "rating": 4.7},
            {"name": "Specialized Solutions Ltd", "tier": VendorTier.TIER_2, "rating": 4.4}
        ]
        
        for i, template in enumerate(vendor_templates):
            vendor = VendorProfile(
                vendor_id=f"vendor_{i}_{template['name'].lower().replace(' ', '_')}",
                name=template["name"],
                tier=template["tier"],
                contact_info={"email": f"contact@{template['name'].lower().replace(' ', '')}.com"},
                capabilities=["IT Solutions", "System Integration"],
                regions_served=["Global" if template["tier"] == VendorTier.TIER_1 else "Regional"],
                rating=template["rating"],
                pricing_model="competitive"
            )
            vendors.append(vendor)
        
        return vendors
    
    def _score_vendor(self, vendor: VendorProfile, state: VendorDiscoveryState) -> float:
        """Score vendor match to requirements"""
        score = 0.0
        
        # Rating component (40%)
        score += (vendor.rating / 5.0) * 0.4
        
        # Tier preference (30%)
        tier_scores = {VendorTier.TIER_1: 0.9, VendorTier.TIER_2: 1.0, VendorTier.TIER_3: 0.8}
        score += tier_scores.get(vendor.tier, 0.5) * 0.3
        
        # Geographic fit (20%)
        if state.region.lower() in str(vendor.regions_served).lower():
            score += 0.2
        else:
            score += 0.1
        
        # Base capability (10%)
        score += 0.1
        
        return min(1.0, score)
    
    def _get_recommendation_reason(self, vendor: VendorProfile, score: float) -> str:
        """Generate recommendation reason"""
        if score >= 0.8:
            return f"Excellent match - {vendor.tier.value} vendor with {vendor.rating}/5 rating"
        elif score >= 0.6:
            return f"Good option - {vendor.tier.value} vendor with solid capabilities"
        else:
            return f"Consider for evaluation - {vendor.tier.value} vendor requiring assessment"
    
    async def discover_vendors(self, category: str, region: str, budget_range: Optional[str] = None) -> Dict[str, Any]:
        """Main entry point for vendor discovery"""
        try:
            state = VendorDiscoveryState(
                category=category,
                region=region,
                budget_range=budget_range
            )
            
            final_state = await self.workflow.ainvoke(state)
            
            return {
                "success": True,
                "category": category,
                "region": region,
                "total_vendors": final_state.total_vendors_found,
                "tier_1_vendors": [v.__dict__ for v in final_state.tier_1_vendors],
                "tier_2_vendors": [v.__dict__ for v in final_state.tier_2_vendors],
                "tier_3_vendors": [v.__dict__ for v in final_state.tier_3_vendors],
                "recommendations": final_state.vendor_recommendations,
                "quality_score": final_state.discovery_quality_score,
                "agent_id": self.agent_id
            }
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"category": category, "region": region},
                execution_id=f"vendor_discovery_{datetime.utcnow().timestamp()}",
                step_name="discover_vendors"
            )
            
            return {
                "success": False,
                "error": error_context,
                "agent_id": self.agent_id
            }
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """Return agent capabilities"""
        return {
            "agent_id": self.agent_id,
            "agent_type": "vendor_discovery",
            "capabilities": ["vendor_identification", "vendor_qualification", "market_mapping"],
            "description": "Specialized agent for vendor discovery and qualification",
            "version": "1.0.0",
            "factor_compliance": ["Factor 10: Small, Focused Agents"]
        }