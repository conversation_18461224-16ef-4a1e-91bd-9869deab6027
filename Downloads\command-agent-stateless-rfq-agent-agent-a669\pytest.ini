[tool:pytest]
# Pytest configuration for LangGraph AI Agent System
minversion = 6.0
addopts = 
    -ra 
    -q 
    --strict-markers 
    --strict-config
    --cov=services/agent
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --maxfail=10
    --tb=short
    -p no:warnings

testpaths = tests

python_files = test_*.py *_test.py
python_classes = Test* *Test
python_functions = test_*

# Custom markers for test organization
markers =
    unit: Unit tests - fast, isolated tests
    integration: Integration tests - test component interactions
    e2e: End-to-end tests - full system tests
    slow: Slow running tests
    factor1: Tests for Factor 1 - Natural Language to Tool Calls
    factor2: Tests for Factor 2 - Own Your Prompts  
    factor3: Tests for Factor 3 - Own Your Context Window
    factor4: Tests for Factor 4 - Tools Are Just Structured Outputs
    factor5: Tests for Factor 5 - Unify Execution State and Business State
    factor6: Tests for Factor 6 - Launch/Pause/Resume
    factor7: Tests for Factor 7 - Contact Humans with Tool Calls
    factor8: Tests for Factor 8 - Own Your Control Flow
    factor9: Tests for Factor 9 - Compact Errors into Context Window
    factor10: Tests for Factor 10 - Small, Focused Agents
    factor11: Tests for Factor 11 - Trigger from Anywhere
    factor12: Tests for Factor 12 - Make Your Agent a Stateless Reducer

# Test filtering options
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:asyncio

# Async test configuration
asyncio_mode = auto

# Test discovery patterns
norecursedirs = 
    .git
    .tox
    dist
    build
    south_migrations
    __pycache__
    .pytest_cache
    node_modules
    venv
    env

# Console output configuration
console_output_style = progress
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration
[coverage:run]
source = services/agent
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*
    */node_modules/*
    setup.py
    manage.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

precision = 2
show_missing = true
skip_covered = false

[coverage:html]
directory = htmlcov
title = LangGraph AI Agent Test Coverage

[coverage:xml]
output = coverage.xml