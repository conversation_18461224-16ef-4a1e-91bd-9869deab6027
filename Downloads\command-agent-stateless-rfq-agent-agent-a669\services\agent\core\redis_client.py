"""
Redis client module for caching and state management
Implements high-performance caching layer for AI agent system
"""

import redis.asyncio as redis
import json
import pickle
import structlog
from typing import Any, Optional, Dict, List, Union
from datetime import timedelta
import asyncio
from contextlib import asynccontextmanager

from .config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

# Global Redis client
_redis_client: Optional[redis.Redis] = None

async def init_redis():
    """Initialize Redis connection"""
    global _redis_client
    
    try:
        _redis_client = redis.from_url(
            settings.redis_url,
            max_connections=settings.redis_max_connections,
            decode_responses=False,  # We'll handle encoding ourselves
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30
        )
        
        # Test connection
        await _redis_client.ping()
        logger.info("Redis initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize Redis", error=str(e))
        raise

async def close_redis():
    """Close Redis connection"""
    global _redis_client
    
    if _redis_client:
        await _redis_client.close()
        logger.info("Redis connection closed")

async def get_redis() -> redis.Redis:
    """Get Redis client instance"""
    if not _redis_client:
        raise RuntimeError("Redis not initialized. Call init_redis() first.")
    
    return _redis_client

class RedisService:
    """Redis service for caching and state management"""
    
    def __init__(self):
        self.client = None
    
    async def get_client(self) -> redis.Redis:
        """Get Redis client"""
        if not self.client:
            self.client = await get_redis()
        return self.client
    
    # ========== BASIC OPERATIONS ==========
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ex: Optional[Union[int, timedelta]] = None,
        serialization: str = "json"
    ) -> bool:
        """Set a key with optional expiration"""
        client = await self.get_client()
        
        if serialization == "json":
            serialized_value = json.dumps(value, default=str)
        elif serialization == "pickle":
            serialized_value = pickle.dumps(value)
        else:
            serialized_value = str(value)
        
        return await client.set(key, serialized_value, ex=ex)
    
    async def get(
        self, 
        key: str, 
        serialization: str = "json"
    ) -> Any:
        """Get a value by key"""
        client = await self.get_client()
        value = await client.get(key)
        
        if value is None:
            return None
        
        if serialization == "json":
            return json.loads(value.decode('utf-8'))
        elif serialization == "pickle":
            return pickle.loads(value)
        else:
            return value.decode('utf-8')
    
    async def delete(self, *keys: str) -> int:
        """Delete one or more keys"""
        client = await self.get_client()
        return await client.delete(*keys)
    
    async def exists(self, *keys: str) -> int:
        """Check if keys exist"""
        client = await self.get_client()
        return await client.exists(*keys)
    
    async def expire(self, key: str, time: Union[int, timedelta]) -> bool:
        """Set expiration for a key"""
        client = await self.get_client()
        return await client.expire(key, time)
    
    async def ttl(self, key: str) -> int:
        """Get time to live for a key"""
        client = await self.get_client()
        return await client.ttl(key)
    
    # ========== HASH OPERATIONS ==========
    
    async def hset(self, name: str, mapping: Dict[str, Any]) -> int:
        """Set hash fields"""
        client = await self.get_client()
        serialized_mapping = {
            k: json.dumps(v, default=str) if not isinstance(v, str) else v
            for k, v in mapping.items()
        }
        return await client.hset(name, mapping=serialized_mapping)
    
    async def hget(self, name: str, key: str) -> Any:
        """Get hash field value"""
        client = await self.get_client()
        value = await client.hget(name, key)
        if value:
            try:
                return json.loads(value.decode('utf-8'))
            except json.JSONDecodeError:
                return value.decode('utf-8')
        return None
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """Get all hash fields and values"""
        client = await self.get_client()
        data = await client.hgetall(name)
        result = {}
        for k, v in data.items():
            try:
                result[k.decode('utf-8')] = json.loads(v.decode('utf-8'))
            except json.JSONDecodeError:
                result[k.decode('utf-8')] = v.decode('utf-8')
        return result
    
    async def hdel(self, name: str, *keys: str) -> int:
        """Delete hash fields"""
        client = await self.get_client()
        return await client.hdel(name, *keys)
    
    # ========== LIST OPERATIONS ==========
    
    async def lpush(self, name: str, *values: Any) -> int:
        """Push values to the left of a list"""
        client = await self.get_client()
        serialized_values = [
            json.dumps(v, default=str) if not isinstance(v, str) else v
            for v in values
        ]
        return await client.lpush(name, *serialized_values)
    
    async def rpush(self, name: str, *values: Any) -> int:
        """Push values to the right of a list"""
        client = await self.get_client()
        serialized_values = [
            json.dumps(v, default=str) if not isinstance(v, str) else v
            for v in values
        ]
        return await client.rpush(name, *serialized_values)
    
    async def lpop(self, name: str) -> Any:
        """Pop value from the left of a list"""
        client = await self.get_client()
        value = await client.lpop(name)
        if value:
            try:
                return json.loads(value.decode('utf-8'))
            except json.JSONDecodeError:
                return value.decode('utf-8')
        return None
    
    async def rpop(self, name: str) -> Any:
        """Pop value from the right of a list"""
        client = await self.get_client()
        value = await client.rpop(name)
        if value:
            try:
                return json.loads(value.decode('utf-8'))
            except json.JSONDecodeError:
                return value.decode('utf-8')
        return None
    
    async def lrange(self, name: str, start: int, end: int) -> List[Any]:
        """Get a range of values from a list"""
        client = await self.get_client()
        values = await client.lrange(name, start, end)
        result = []
        for value in values:
            try:
                result.append(json.loads(value.decode('utf-8')))
            except json.JSONDecodeError:
                result.append(value.decode('utf-8'))
        return result
    
    # ========== SET OPERATIONS ==========
    
    async def sadd(self, name: str, *values: Any) -> int:
        """Add values to a set"""
        client = await self.get_client()
        serialized_values = [
            json.dumps(v, default=str) if not isinstance(v, str) else str(v)
            for v in values
        ]
        return await client.sadd(name, *serialized_values)
    
    async def smembers(self, name: str) -> set:
        """Get all members of a set"""
        client = await self.get_client()
        values = await client.smembers(name)
        result = set()
        for value in values:
            try:
                result.add(json.loads(value.decode('utf-8')))
            except json.JSONDecodeError:
                result.add(value.decode('utf-8'))
        return result
    
    async def srem(self, name: str, *values: Any) -> int:
        """Remove values from a set"""
        client = await self.get_client()
        serialized_values = [
            json.dumps(v, default=str) if not isinstance(v, str) else str(v)
            for v in values
        ]
        return await client.srem(name, *serialized_values)

# Cache decorators and utilities
class CacheManager:
    """Cache manager for common caching patterns"""
    
    def __init__(self, redis_service: RedisService):
        self.redis = redis_service
    
    async def cached_function(
        self,
        cache_key: str,
        func,
        *args,
        ttl: int = 3600,
        **kwargs
    ):
        """Cache function result"""
        # Check cache first
        cached_result = await self.redis.get(cache_key)
        if cached_result is not None:
            logger.debug("Cache hit", cache_key=cache_key)
            return cached_result
        
        # Execute function and cache result
        logger.debug("Cache miss, executing function", cache_key=cache_key)
        result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
        
        await self.redis.set(cache_key, result, ex=ttl)
        return result
    
    async def invalidate_pattern(self, pattern: str):
        """Invalidate all keys matching a pattern"""
        client = await self.redis.get_client()
        keys = await client.keys(pattern)
        if keys:
            await client.delete(*keys)
            logger.info(f"Invalidated {len(keys)} cache keys", pattern=pattern)

# Workflow state management
class WorkflowStateManager:
    """Manage workflow state in Redis"""
    
    def __init__(self, redis_service: RedisService):
        self.redis = redis_service
        self.state_prefix = "workflow:state:"
        self.checkpoint_prefix = "workflow:checkpoint:"
    
    async def save_state(
        self, 
        execution_id: str, 
        state: Dict[str, Any],
        ttl: int = 86400  # 24 hours
    ):
        """Save workflow state"""
        key = f"{self.state_prefix}{execution_id}"
        await self.redis.set(key, state, ex=ttl)
        logger.debug("Workflow state saved", execution_id=execution_id)
    
    async def load_state(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Load workflow state"""
        key = f"{self.state_prefix}{execution_id}"
        state = await self.redis.get(key)
        if state:
            logger.debug("Workflow state loaded", execution_id=execution_id)
        return state
    
    async def delete_state(self, execution_id: str):
        """Delete workflow state"""
        key = f"{self.state_prefix}{execution_id}"
        await self.redis.delete(key)
        logger.debug("Workflow state deleted", execution_id=execution_id)
    
    async def create_checkpoint(
        self, 
        execution_id: str, 
        step_name: str, 
        checkpoint_data: Dict[str, Any]
    ):
        """Create a workflow checkpoint"""
        key = f"{self.checkpoint_prefix}{execution_id}:{step_name}"
        await self.redis.set(key, checkpoint_data, ex=86400)  # 24 hours
        
        # Add to checkpoint list
        checkpoint_list_key = f"{self.checkpoint_prefix}list:{execution_id}"
        await self.redis.lpush(checkpoint_list_key, step_name)
        await self.redis.expire(checkpoint_list_key, 86400)
        
        logger.debug("Checkpoint created", 
                    execution_id=execution_id, 
                    step_name=step_name)
    
    async def load_checkpoint(
        self, 
        execution_id: str, 
        step_name: str
    ) -> Optional[Dict[str, Any]]:
        """Load a specific checkpoint"""
        key = f"{self.checkpoint_prefix}{execution_id}:{step_name}"
        return await self.redis.get(key)
    
    async def list_checkpoints(self, execution_id: str) -> List[str]:
        """List all checkpoints for an execution"""
        checkpoint_list_key = f"{self.checkpoint_prefix}list:{execution_id}"
        return await self.redis.lrange(checkpoint_list_key, 0, -1)

# Session management
class SessionManager:
    """Manage user sessions and context"""
    
    def __init__(self, redis_service: RedisService):
        self.redis = redis_service
        self.session_prefix = "session:"
        self.context_prefix = "context:"
    
    async def create_session(
        self, 
        user_id: str, 
        session_data: Dict[str, Any],
        ttl: int = 3600
    ) -> str:
        """Create a new session"""
        import uuid
        session_id = str(uuid.uuid4())
        key = f"{self.session_prefix}{session_id}"
        
        session_info = {
            "user_id": user_id,
            "created_at": json.dumps(None, default=str),  # Will be handled by JSON serialization
            **session_data
        }
        
        await self.redis.set(key, session_info, ex=ttl)
        logger.info("Session created", session_id=session_id, user_id=user_id)
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        key = f"{self.session_prefix}{session_id}"
        return await self.redis.get(key)
    
    async def update_session(
        self, 
        session_id: str, 
        updates: Dict[str, Any]
    ):
        """Update session data"""
        key = f"{self.session_prefix}{session_id}"
        current_data = await self.redis.get(key)
        if current_data:
            current_data.update(updates)
            await self.redis.set(key, current_data)
            # Preserve existing TTL
            ttl = await self.redis.ttl(key)
            if ttl > 0:
                await self.redis.expire(key, ttl)
    
    async def delete_session(self, session_id: str):
        """Delete a session"""
        key = f"{self.session_prefix}{session_id}"
        await self.redis.delete(key)
        logger.info("Session deleted", session_id=session_id)

# Export service instances
redis_service = RedisService()
cache_manager = CacheManager(redis_service)
workflow_state_manager = WorkflowStateManager(redis_service)
session_manager = SessionManager(redis_service)