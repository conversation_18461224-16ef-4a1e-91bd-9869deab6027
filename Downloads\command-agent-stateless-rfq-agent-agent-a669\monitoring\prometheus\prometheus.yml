global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # AI Agent Service
  - job_name: 'ai-agent-service'
    static_configs:
      - targets: ['agent-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # PostgreSQL Database
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # RabbitMQ Message Queue
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Docker Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # Nginx Reverse Proxy
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # Custom Application Metrics
  - job_name: 'langgraph-workflows'
    static_configs:
      - targets: ['agent-service:8000']
    metrics_path: '/api/metrics/workflows'
    scrape_interval: 30s

  - job_name: 'memory-service-metrics'
    static_configs:
      - targets: ['agent-service:8000']  
    metrics_path: '/api/metrics/memory'
    scrape_interval: 30s

  - job_name: 'agent-performance'
    static_configs:
      - targets: ['agent-service:8000']
    metrics_path: '/api/metrics/agents'
    scrape_interval: 20s