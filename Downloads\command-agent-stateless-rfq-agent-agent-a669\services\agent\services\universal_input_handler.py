"""
Factor 11: Trigger from Anywhere - Universal Input Handler
Handles input from any source/format and routes to appropriate workflow
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import json
import logging
import re
from enum import Enum

from .perplexity_service import PerplexityService
from .memory_service import MemoryService
from .error_manager import ErrorManager
from ..agents.multi_agent_orchestrator import MultiAgentOrchestrator

logger = logging.getLogger(__name__)

class InputSource(Enum):
    """Supported input sources"""
    EMAIL = "email"
    API = "api"
    WEBHOOK = "webhook"
    FILE_UPLOAD = "file_upload"
    CHAT = "chat"
    VOICE = "voice"
    SMS = "sms"
    SLACK = "slack"
    TEAMS = "teams"
    WEB_FORM = "web_form"
    UNKNOWN = "unknown"

class InputFormat(Enum):
    """Supported input formats"""
    TEXT = "text"
    JSON = "json"
    XML = "xml"
    PDF = "pdf"
    EMAIL_MSG = "email_msg"
    VOICE_AUDIO = "voice_audio"
    CSV = "csv"
    EXCEL = "excel"
    UNKNOWN = "unknown"

@dataclass
class InputContext:
    """Context information about the input"""
    source: InputSource
    format: InputFormat
    timestamp: datetime
    sender_info: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    priority: str = "medium"
    requires_response: bool = True

@dataclass
class ParsedRequest:
    """Structured representation of parsed request"""
    intent: str  # rfq_creation, vendor_inquiry, status_check, etc.
    category: str
    region: str
    budget_range: Optional[str] = None
    urgency: str = "medium"
    department: str = "procurement"
    raw_request: str = ""
    confidence_score: float = 0.0
    extracted_entities: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.extracted_entities is None:
            self.extracted_entities = {}

class UniversalInputHandler:
    """
    Factor 11: Universal Input Handler
    
    Capabilities:
    - Accept input from any source (email, API, chat, voice, etc.)
    - Parse any format (text, JSON, XML, PDF, etc.)
    - Extract intent and entities using AI
    - Route to appropriate workflow
    - Handle multi-language input
    - Support structured and unstructured data
    """
    
    def __init__(self):
        self.perplexity = PerplexityService()
        self.memory = MemoryService()
        self.error_manager = ErrorManager()
        self.orchestrator = MultiAgentOrchestrator()
        self.handler_id = "universal_input_handler"
        
        # Intent patterns for classification
        self.intent_patterns = {
            "rfq_creation": [
                r"request for quotation",
                r"rfq",
                r"need quotes?",
                r"procurement",
                r"buy\s+\d+",
                r"purchase"
            ],
            "vendor_inquiry": [
                r"vendor",
                r"supplier",
                r"who can provide",
                r"recommendations"
            ],
            "status_check": [
                r"status",
                r"update",
                r"progress",
                r"what.*happening"
            ],
            "price_inquiry": [
                r"price",
                r"cost",
                r"how much",
                r"budget"
            ]
        }
    
    async def process_input(self, raw_input: Union[str, Dict[str, Any], bytes], 
                          context: InputContext) -> Dict[str, Any]:
        """
        Main entry point for universal input processing
        
        Args:
            raw_input: Input in any format
            context: Context about the input source/format
            
        Returns:
            Processed response with routing information
        """
        try:
            logger.info(f"Processing input from {context.source.value} in {context.format.value} format")
            
            # Step 1: Normalize input to text
            normalized_text = await self._normalize_input(raw_input, context)
            
            # Step 2: Parse and extract intent
            parsed_request = await self._parse_request(normalized_text, context)
            
            # Step 3: Validate and enrich request
            enriched_request = await self._enrich_request(parsed_request, context)
            
            # Step 4: Route to appropriate workflow
            workflow_response = await self._route_to_workflow(enriched_request, context)
            
            # Step 5: Format response for source
            formatted_response = await self._format_response(workflow_response, context)
            
            # Store interaction for learning
            await self._store_interaction(raw_input, context, enriched_request, workflow_response)
            
            return {
                "success": True,
                "input_processed": True,
                "source": context.source.value,
                "format": context.format.value,
                "intent": enriched_request.intent,
                "confidence": enriched_request.confidence_score,
                "workflow_response": workflow_response,
                "formatted_response": formatted_response,
                "handler_id": self.handler_id,
                "processing_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={
                    "raw_input": str(raw_input)[:200],
                    "source": context.source.value,
                    "format": context.format.value
                },
                execution_id=f"input_handler_{datetime.utcnow().timestamp()}",
                step_name="process_input"
            )
            
            return {
                "success": False,
                "error": error_context,
                "handler_id": self.handler_id
            }
    
    async def _normalize_input(self, raw_input: Union[str, Dict[str, Any], bytes], 
                             context: InputContext) -> str:
        """Normalize input from any format to text"""
        try:
            if context.format == InputFormat.TEXT:
                return str(raw_input)
            
            elif context.format == InputFormat.JSON:
                if isinstance(raw_input, str):
                    data = json.loads(raw_input)
                else:
                    data = raw_input
                return self._extract_text_from_json(data)
            
            elif context.format == InputFormat.XML:
                return self._extract_text_from_xml(str(raw_input))
            
            elif context.format == InputFormat.EMAIL_MSG:
                return self._extract_text_from_email(raw_input)
            
            elif context.format == InputFormat.PDF:
                return self._extract_text_from_pdf(raw_input)
            
            elif context.format == InputFormat.VOICE_AUDIO:
                return await self._transcribe_audio(raw_input)
            
            elif context.format == InputFormat.CSV:
                return self._extract_text_from_csv(raw_input)
            
            else:
                # Fallback: try to convert to string
                return str(raw_input)
                
        except Exception as e:
            logger.error(f"Input normalization failed: {e}")
            return str(raw_input)[:1000]  # Truncate if too long
    
    def _extract_text_from_json(self, data: Dict[str, Any]) -> str:
        """Extract text content from JSON structure"""
        if isinstance(data, dict):
            # Look for common text fields
            text_fields = ["message", "content", "body", "text", "description", "request"]
            for field in text_fields:
                if field in data and isinstance(data[field], str):
                    return data[field]
            
            # Concatenate all string values
            text_parts = []
            for key, value in data.items():
                if isinstance(value, str):
                    text_parts.append(f"{key}: {value}")
            return " ".join(text_parts)
        
        return str(data)
    
    def _extract_text_from_xml(self, xml_content: str) -> str:
        """Extract text content from XML"""
        # Simple XML text extraction (in production, use proper XML parser)
        import re
        text = re.sub(r'<[^>]+>', ' ', xml_content)
        return ' '.join(text.split())
    
    def _extract_text_from_email(self, email_data: Union[str, Dict[str, Any]]) -> str:
        """Extract text from email message"""
        if isinstance(email_data, dict):
            subject = email_data.get("subject", "")
            body = email_data.get("body", "")
            return f"Subject: {subject}\\n\\nBody: {body}"
        return str(email_data)
    
    def _extract_text_from_pdf(self, pdf_data: bytes) -> str:
        """Extract text from PDF (placeholder - requires PDF library)"""
        return "PDF text extraction not implemented - would use PyPDF2 or similar"
    
    async def _transcribe_audio(self, audio_data: bytes) -> str:
        """Transcribe audio to text (placeholder - requires speech recognition)"""
        return "Audio transcription not implemented - would use speech-to-text API"
    
    def _extract_text_from_csv(self, csv_data: str) -> str:
        """Extract meaningful text from CSV data"""
        lines = csv_data.split('\\n')
        if lines:
            # Return first few rows as context
            return " ".join(lines[:5])
        return csv_data
    
    async def _parse_request(self, text: str, context: InputContext) -> ParsedRequest:
        """Parse text to extract intent and entities"""
        try:
            # Use Perplexity for intelligent parsing
            parsing_prompt = f"""
            Parse this procurement-related request and extract structured information:
            
            Text: "{text}"
            Source: {context.source.value}
            
            Extract:
            1. Intent (rfq_creation, vendor_inquiry, status_check, price_inquiry, other)
            2. Product/service category
            3. Geographic region/location
            4. Budget range (if mentioned)
            5. Urgency level (low, medium, high, urgent)
            6. Department (if mentioned)
            7. Key entities and details
            
            Respond in JSON format with extracted information.
            If information is unclear, make reasonable assumptions based on context.
            """
            
            parsed_response = await self.perplexity.generate_response(
                prompt=parsing_prompt,
                context="request_parsing"
            )
            
            # Also use pattern matching as fallback
            pattern_intent = self._classify_intent_by_patterns(text)
            
            # Extract entities using regex and AI
            extracted_entities = await self._extract_entities(text)
            
            # Combine AI parsing with pattern matching
            parsed_request = ParsedRequest(
                intent=pattern_intent,
                category=extracted_entities.get("category", "general"),
                region=extracted_entities.get("region", "unspecified"),
                budget_range=extracted_entities.get("budget_range"),
                urgency=extracted_entities.get("urgency", "medium"),
                department=extracted_entities.get("department", "procurement"),
                raw_request=text,
                confidence_score=0.8,  # Would calculate based on parsing quality
                extracted_entities=extracted_entities
            )
            
            return parsed_request
            
        except Exception as e:
            logger.error(f"Request parsing failed: {e}")
            
            # Fallback parsing
            return ParsedRequest(
                intent="rfq_creation",
                category="general",
                region="unspecified",
                raw_request=text,
                confidence_score=0.3
            )
    
    def _classify_intent_by_patterns(self, text: str) -> str:
        """Classify intent using regex patterns"""
        text_lower = text.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    return intent
        
        return "rfq_creation"  # Default intent
    
    async def _extract_entities(self, text: str) -> Dict[str, Any]:
        """Extract entities from text using AI and patterns"""
        entities = {}
        
        # Extract common entities with regex
        # Budget patterns
        budget_pattern = r'budget[:\s]*\$?([0-9,]+(?:\.[0-9]{2})?)'
        budget_match = re.search(budget_pattern, text, re.IGNORECASE)
        if budget_match:
            entities["budget_range"] = f"${budget_match.group(1)}"
        
        # Quantity patterns
        quantity_pattern = r'(\d+)\s*(units?|pieces?|items?)'
        quantity_match = re.search(quantity_pattern, text, re.IGNORECASE)
        if quantity_match:
            entities["quantity"] = quantity_match.group(1)
        
        # Region patterns
        region_patterns = [
            r'\b(US|USA|United States|America)\b',
            r'\b(UK|United Kingdom|Britain)\b',
            r'\b(India|Mumbai|Delhi|Bangalore|Chennai)\b',
            r'\b(China|Beijing|Shanghai)\b'
        ]
        for pattern in region_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                entities["region"] = re.search(pattern, text, re.IGNORECASE).group(1)
                break
        
        # Category patterns
        category_patterns = {
            "IT": r'\b(computer|laptop|server|software|IT|technology)\b',
            "furniture": r'\b(desk|chair|furniture|office furniture)\b',
            "construction": r'\b(construction|building|materials)\b',
            "medical": r'\b(medical|healthcare|equipment)\b'
        }
        for category, pattern in category_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                entities["category"] = category
                break
        
        # Urgency patterns
        urgency_patterns = {
            "urgent": r'\b(urgent|asap|immediately|rush)\b',
            "high": r'\b(high priority|soon|quickly)\b',
            "low": r'\b(low priority|whenever|no rush)\b'
        }
        for urgency, pattern in urgency_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                entities["urgency"] = urgency
                break
        
        return entities
    
    async def _enrich_request(self, parsed_request: ParsedRequest, 
                            context: InputContext) -> ParsedRequest:
        """Enrich request with additional context and validation"""
        try:
            # Use memory to find similar past requests
            similar_requests = await self.memory.search_memories(
                query=parsed_request.raw_request,
                agent_id=self.handler_id,
                limit=3
            )
            
            # Enrich with historical patterns
            if similar_requests:
                # Use patterns from similar requests to fill gaps
                for memory in similar_requests:
                    if not parsed_request.budget_range and "budget" in memory.content:
                        # Extract budget from similar request
                        pass
            
            # Validate and set defaults
            if not parsed_request.category or parsed_request.category == "general":
                parsed_request.category = "unspecified"
            
            if not parsed_request.region or parsed_request.region == "unspecified":
                parsed_request.region = self._infer_region_from_context(context)
            
            # Increase confidence if we found good matches
            if similar_requests:
                parsed_request.confidence_score = min(1.0, parsed_request.confidence_score + 0.2)
            
            return parsed_request
            
        except Exception as e:
            logger.error(f"Request enrichment failed: {e}")
            return parsed_request
    
    def _infer_region_from_context(self, context: InputContext) -> str:
        """Infer region from context information"""
        if context.sender_info:
            # Check sender location, timezone, etc.
            if "timezone" in context.sender_info:
                tz = context.sender_info["timezone"]
                if "US" in tz or "America" in tz:
                    return "United States"
                elif "Europe" in tz:
                    return "Europe"
                elif "Asia" in tz:
                    return "Asia"
        
        return "Global"  # Default
    
    async def _route_to_workflow(self, parsed_request: ParsedRequest, 
                                context: InputContext) -> Dict[str, Any]:
        """Route parsed request to appropriate workflow"""
        try:
            if parsed_request.intent == "rfq_creation":
                # Route to multi-agent RFQ workflow
                return await self.orchestrator.process_rfq_request(
                    rfq_request=parsed_request.raw_request,
                    region=parsed_request.region,
                    category=parsed_request.category,
                    budget_range=parsed_request.budget_range,
                    urgency=parsed_request.urgency,
                    department=parsed_request.department
                )
            
            elif parsed_request.intent == "status_check":
                return await self._handle_status_check(parsed_request)
            
            elif parsed_request.intent == "vendor_inquiry":
                return await self._handle_vendor_inquiry(parsed_request)
            
            elif parsed_request.intent == "price_inquiry":
                return await self._handle_price_inquiry(parsed_request)
            
            else:
                return {
                    "success": False,
                    "error": f"Unsupported intent: {parsed_request.intent}",
                    "suggested_action": "Please clarify your request"
                }
                
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"parsed_request": parsed_request.__dict__},
                execution_id=f"routing_{datetime.utcnow().timestamp()}",
                step_name="route_to_workflow"
            )
            
            return {
                "success": False,
                "error": error_context
            }
    
    async def _handle_status_check(self, parsed_request: ParsedRequest) -> Dict[str, Any]:
        """Handle status check requests"""
        return {
            "success": True,
            "response_type": "status_check",
            "message": "Status check functionality would be implemented here"
        }
    
    async def _handle_vendor_inquiry(self, parsed_request: ParsedRequest) -> Dict[str, Any]:
        """Handle vendor inquiry requests"""
        # Use vendor discovery agent
        from ..agents.vendor_discovery_agent import VendorDiscoveryAgent
        vendor_agent = VendorDiscoveryAgent()
        
        return await vendor_agent.discover_vendors(
            category=parsed_request.category,
            region=parsed_request.region,
            budget_range=parsed_request.budget_range
        )
    
    async def _handle_price_inquiry(self, parsed_request: ParsedRequest) -> Dict[str, Any]:
        """Handle price inquiry requests"""
        # Use market research agent
        from ..agents.market_research_agent import MarketResearchAgent
        market_agent = MarketResearchAgent()
        
        return await market_agent.research_market(
            query=parsed_request.raw_request,
            region=parsed_request.region,
            category=parsed_request.category,
            budget_range=parsed_request.budget_range
        )
    
    async def _format_response(self, workflow_response: Dict[str, Any], 
                             context: InputContext) -> Dict[str, Any]:
        """Format response based on input source"""
        try:
            base_response = {
                "timestamp": datetime.utcnow().isoformat(),
                "source": context.source.value,
                "success": workflow_response.get("success", False)
            }
            
            if context.source == InputSource.EMAIL:
                return self._format_email_response(workflow_response, base_response)
            elif context.source == InputSource.API:
                return self._format_api_response(workflow_response, base_response)
            elif context.source == InputSource.CHAT:
                return self._format_chat_response(workflow_response, base_response)
            elif context.source == InputSource.SLACK:
                return self._format_slack_response(workflow_response, base_response)
            else:
                return {**base_response, "data": workflow_response}
                
        except Exception as e:
            logger.error(f"Response formatting failed: {e}")
            return {"error": "Response formatting failed", "raw_response": workflow_response}
    
    def _format_email_response(self, workflow_response: Dict[str, Any], 
                             base_response: Dict[str, Any]) -> Dict[str, Any]:
        """Format response for email"""
        if workflow_response.get("success"):
            subject = "RFQ Processing Complete"
            body = f"Your RFQ request has been processed successfully. Workflow ID: {workflow_response.get('workflow_id', 'N/A')}"
        else:
            subject = "RFQ Processing Failed"
            body = f"There was an issue processing your RFQ: {workflow_response.get('error', 'Unknown error')}"
        
        return {
            **base_response,
            "email_format": {
                "subject": subject,
                "body": body,
                "attachments": []
            }
        }
    
    def _format_api_response(self, workflow_response: Dict[str, Any], 
                           base_response: Dict[str, Any]) -> Dict[str, Any]:
        """Format response for API"""
        return {
            **base_response,
            "data": workflow_response,
            "format": "json"
        }
    
    def _format_chat_response(self, workflow_response: Dict[str, Any], 
                            base_response: Dict[str, Any]) -> Dict[str, Any]:
        """Format response for chat"""
        if workflow_response.get("success"):
            message = f"✅ RFQ processed successfully! Found {workflow_response.get('summary', {}).get('total_vendors_found', 0)} vendors."
        else:
            message = f"❌ RFQ processing failed: {workflow_response.get('error', 'Unknown error')}"
        
        return {
            **base_response,
            "chat_format": {
                "message": message,
                "quick_replies": ["View Details", "New Request", "Help"]
            }
        }
    
    def _format_slack_response(self, workflow_response: Dict[str, Any], 
                             base_response: Dict[str, Any]) -> Dict[str, Any]:
        """Format response for Slack"""
        return {
            **base_response,
            "slack_format": {
                "text": "RFQ Processing Complete" if workflow_response.get("success") else "RFQ Processing Failed",
                "blocks": [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*Status:* {'Success' if workflow_response.get('success') else 'Failed'}"
                        }
                    }
                ]
            }
        }
    
    async def _store_interaction(self, raw_input: Union[str, Dict[str, Any], bytes],
                               context: InputContext, parsed_request: ParsedRequest,
                               workflow_response: Dict[str, Any]):
        """Store interaction for learning and improvement"""
        try:
            interaction_data = {
                "raw_input": str(raw_input)[:500],  # Truncate for storage
                "source": context.source.value,
                "format": context.format.value,
                "parsed_intent": parsed_request.intent,
                "parsed_category": parsed_request.category,
                "confidence_score": parsed_request.confidence_score,
                "workflow_success": workflow_response.get("success", False),
                "processing_timestamp": datetime.utcnow().isoformat()
            }
            
            await self.memory.store_conversation_memory(
                agent_id=self.handler_id,
                content=f"Input interaction: {json.dumps(interaction_data)}",
                memory_type="input_interactions",
                metadata={
                    "source": context.source.value,
                    "intent": parsed_request.intent,
                    "success": workflow_response.get("success", False)
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to store interaction: {e}")
    
    def get_handler_capabilities(self) -> Dict[str, Any]:
        """Return handler capabilities"""
        return {
            "handler_id": self.handler_id,
            "handler_type": "universal_input",
            "supported_sources": [source.value for source in InputSource],
            "supported_formats": [format.value for format in InputFormat],
            "supported_intents": list(self.intent_patterns.keys()),
            "capabilities": [
                "multi_source_input",
                "multi_format_parsing",
                "intent_classification",
                "entity_extraction",
                "workflow_routing",
                "response_formatting"
            ],
            "description": "Universal input handler supporting any source and format",
            "version": "1.0.0",
            "factor_compliance": ["Factor 11: Trigger from Anywhere"]
        }