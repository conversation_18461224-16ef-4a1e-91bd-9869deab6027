"""
Perplexity API service for AI-powered research and market analysis
Implements Factor 1: Natural Language to Tool Calls with structured outputs
"""

import httpx
import structlog
import json
from typing import Dict, Any, List, Optional
import asyncio
from datetime import datetime

from ..core.config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

class PerplexityService:
    """Service for interacting with Perplexity API"""
    
    def __init__(self):
        self.api_key = settings.perplexity_api_key
        self.base_url = "https://api.perplexity.ai"
        self.model = settings.perplexity_model
        self.max_tokens = settings.perplexity_max_tokens
        self.temperature = settings.perplexity_temperature
    
    async def _make_request(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Make request to Perplexity API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": f"perplexity/{self.model}",
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "stream": False
        }
        
        if tools:
            payload["tools"] = tools
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()
                
        except httpx.HTTPError as e:
            logger.error("Perplexity API request failed", error=str(e))
            raise
        except Exception as e:
            logger.error("Unexpected error in Perplexity request", error=str(e))
            raise
    
    async def research_market(self, query: str, context: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Conduct market research using Perplexity
        Implements structured output for market intelligence
        """
        logger.info("Conducting market research", query=query[:100])
        
        # Prepare context from memory if available
        context_text = ""
        if context:
            context_text = "\\n".join([item.get("content", "") for item in context[:3]])
        
        system_prompt = """You are a procurement market research specialist. 
        Analyze the given procurement request and provide comprehensive market intelligence.
        
        Focus on:
        1. Current market prices and price ranges
        2. Market trends and growth patterns
        3. Key suppliers and vendor recommendations
        4. Risk factors and challenges
        5. Compliance and regulatory requirements
        
        Provide specific, actionable insights based on current market data."""
        
        user_prompt = f"""Research market conditions for this procurement request:
        
        Request: {query}
        
        Additional Context: {context_text}
        
        Provide detailed market intelligence including pricing, trends, suppliers, risks, and compliance requirements."""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        # Define structured output tool
        market_research_tool = {
            "type": "function",
            "function": {
                "name": "provide_market_intelligence",
                "description": "Provide structured market intelligence for procurement",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "price_range": {
                            "type": "string",
                            "description": "Estimated price range for the requested items"
                        },
                        "market_trends": {
                            "type": "string",
                            "description": "Current market trends and growth patterns"
                        },
                        "supplier_recommendations": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of recommended suppliers or supplier types"
                        },
                        "risk_factors": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Key risk factors to consider"
                        },
                        "compliance_requirements": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Relevant compliance and regulatory requirements"
                        },
                        "market_insights": {
                            "type": "string",
                            "description": "Additional market insights and recommendations"
                        }
                    },
                    "required": ["price_range", "market_trends", "supplier_recommendations", "risk_factors"]
                }
            }
        }
        
        try:
            response = await self._make_request(messages, tools=[market_research_tool])
            
            # Extract structured data from tool call or content
            if "choices" in response and response["choices"]:
                choice = response["choices"][0]
                
                # Check for tool calls
                if "message" in choice and "tool_calls" in choice["message"]:
                    tool_call = choice["message"]["tool_calls"][0]
                    if tool_call["function"]["name"] == "provide_market_intelligence":
                        structured_data = json.loads(tool_call["function"]["arguments"])
                        return {
                            "content": choice["message"].get("content", ""),
                            "structured_data": structured_data,
                            "timestamp": datetime.utcnow().isoformat()
                        }
                
                # Fallback to content-based parsing
                content = choice["message"]["content"]
                return {
                    "content": content,
                    "structured_data": self._parse_market_intelligence(content),
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            return {
                "content": "Market research completed",
                "structured_data": {},
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Market research failed", error=str(e))
            return {
                "content": f"Market research failed: {str(e)}",
                "structured_data": {},
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }
    
    async def discover_vendors(self, query: str, context: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Discover vendors using Perplexity research
        """
        logger.info("Discovering vendors", query=query[:100])
        
        context_text = ""
        if context:
            context_text = "\\n".join([item.get("content", "") for item in context[:3]])
        
        system_prompt = """You are a vendor discovery specialist. 
        Research and identify potential suppliers for procurement requests.
        
        Focus on:
        1. Reputable suppliers in the specified region
        2. Supplier specialties and capabilities
        3. Supplier ratings and reputation
        4. Contact information when available
        5. Supplier strengths and differentiators"""
        
        user_prompt = f"""Find suppliers for this procurement request:
        
        Request: {query}
        
        Context: {context_text}
        
        Identify specific suppliers with their capabilities, reputation, and contact details if available."""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        vendor_discovery_tool = {
            "type": "function",
            "function": {
                "name": "identify_vendors",
                "description": "Identify and recommend vendors for procurement",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "vendors": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "specialties": {
                                        "type": "array",
                                        "items": {"type": "string"}
                                    },
                                    "location": {"type": "string"},
                                    "reputation": {"type": "string"},
                                    "contact_info": {"type": "string"}
                                }
                            }
                        },
                        "vendor_insights": {
                            "type": "string",
                            "description": "Additional insights about the vendor landscape"
                        }
                    },
                    "required": ["vendors", "vendor_insights"]
                }
            }
        }
        
        try:
            response = await self._make_request(messages, tools=[vendor_discovery_tool])
            
            if "choices" in response and response["choices"]:
                choice = response["choices"][0]
                
                if "message" in choice and "tool_calls" in choice["message"]:
                    tool_call = choice["message"]["tool_calls"][0]
                    if tool_call["function"]["name"] == "identify_vendors":
                        structured_data = json.loads(tool_call["function"]["arguments"])
                        return {
                            "content": choice["message"].get("content", ""),
                            "vendors": structured_data.get("vendors", []),
                            "insights": structured_data.get("vendor_insights", ""),
                            "timestamp": datetime.utcnow().isoformat()
                        }
                
                content = choice["message"]["content"]
                return {
                    "content": content,
                    "vendors": self._parse_vendors(content),
                    "insights": content,
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            return {
                "content": "Vendor discovery completed",
                "vendors": [],
                "insights": "",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Vendor discovery failed", error=str(e))
            return {
                "content": f"Vendor discovery failed: {str(e)}",
                "vendors": [],
                "insights": "",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }
    
    async def generate_summary(self, workflow_data: Dict[str, Any]) -> str:
        """Generate executive summary using Perplexity"""
        logger.info("Generating workflow summary")
        
        system_prompt = """You are an executive summary specialist.
        Create concise, professional summaries of business workflows.
        Focus on key results, metrics, and next steps."""
        
        user_prompt = f"""Create an executive summary for this RFQ workflow:
        
        Workflow Data: {json.dumps(workflow_data, indent=2)[:2000]}
        
        Include key milestones, results, vendor information, and recommended next steps.
        Format as a professional business report."""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = await self._make_request(messages)
            
            if "choices" in response and response["choices"]:
                return response["choices"][0]["message"]["content"]
            
            return "Executive summary generation completed"
            
        except Exception as e:
            logger.error("Summary generation failed", error=str(e))
            return f"Summary generation failed: {str(e)}"
    
    def _parse_market_intelligence(self, content: str) -> Dict[str, Any]:
        """Parse market intelligence from unstructured content"""
        # Simple content parsing - can be enhanced with more sophisticated NLP
        lines = content.split('\\n')
        
        intelligence = {
            "price_range": "Price analysis available",
            "market_trends": "Market trends analysis available",
            "supplier_recommendations": [],
            "risk_factors": [],
            "compliance_requirements": []
        }
        
        current_section = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if "price" in line.lower() and ":" in line:
                intelligence["price_range"] = line.split(":", 1)[1].strip()
            elif "trend" in line.lower() and ":" in line:
                intelligence["market_trends"] = line.split(":", 1)[1].strip()
            elif "supplier" in line.lower() or "vendor" in line.lower():
                if ":" in line:
                    intelligence["supplier_recommendations"].append(line.split(":", 1)[1].strip())
            elif "risk" in line.lower():
                if ":" in line:
                    intelligence["risk_factors"].append(line.split(":", 1)[1].strip())
            elif "compliance" in line.lower() or "regulation" in line.lower():
                if ":" in line:
                    intelligence["compliance_requirements"].append(line.split(":", 1)[1].strip())
        
        return intelligence
    
    def _parse_vendors(self, content: str) -> List[Dict[str, Any]]:
        """Parse vendor information from content"""
        # Simple vendor parsing - can be enhanced
        vendors = []
        lines = content.split('\\n')
        
        current_vendor = {}
        for line in lines:
            line = line.strip()
            if not line:
                if current_vendor:
                    vendors.append(current_vendor)
                    current_vendor = {}
                continue
            
            if "name:" in line.lower():
                current_vendor["name"] = line.split(":", 1)[1].strip()
            elif "location:" in line.lower():
                current_vendor["location"] = line.split(":", 1)[1].strip()
            elif "specialt" in line.lower():
                current_vendor["specialties"] = [line.split(":", 1)[1].strip()]
        
        if current_vendor:
            vendors.append(current_vendor)
        
        return vendors[:5]  # Limit to 5 vendors