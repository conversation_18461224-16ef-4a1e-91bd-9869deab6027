"""
Factor 10: Small, Focused Agents - Multi-Agent Orchestrator
Coordinates multiple specialized agents for comprehensive RFQ processing
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime
import json
import logging
import asyncio
from langgraph import StateGraph
from langgraph.graph import END

from .market_research_agent import MarketResearchAgent, MarketResearchState
from .vendor_discovery_agent import VendorDiscoveryAgent, VendorDiscoveryState
from .document_generation_agent import DocumentGenerationAgent, DocumentRequirements
from ..services.error_manager import ErrorManager
from ..services.memory_service import MemoryService

logger = logging.getLogger(__name__)

@dataclass
class MultiAgentWorkflowState:
    """State for multi-agent orchestration"""
    # Input
    rfq_request: str
    region: str
    category: str
    budget_range: Optional[str] = None
    urgency: str = "medium"
    department: str = "procurement"
    
    # Agent results
    market_research_results: Optional[Dict[str, Any]] = None
    vendor_discovery_results: Optional[Dict[str, Any]] = None
    document_generation_results: Optional[Dict[str, Any]] = None
    
    # Orchestration metadata
    workflow_id: str = ""
    agents_completed: List[str] = None
    overall_quality_score: float = 0.0
    execution_time: float = 0.0
    
    def __post_init__(self):
        if self.agents_completed is None:
            self.agents_completed = []
        if not self.workflow_id:
            self.workflow_id = f"multi_agent_{datetime.utcnow().timestamp()}"

class MultiAgentOrchestrator:
    """
    Factor 10: Multi-Agent Orchestrator
    
    Coordinates specialized agents for comprehensive RFQ processing:
    - MarketResearchAgent: Market intelligence and pricing
    - VendorDiscoveryAgent: Vendor identification and qualification
    - DocumentGenerationAgent: RFQ document creation
    """
    
    def __init__(self):
        # Initialize specialized agents
        self.market_research_agent = MarketResearchAgent()
        self.vendor_discovery_agent = VendorDiscoveryAgent()
        self.document_generation_agent = DocumentGenerationAgent()
        
        # Infrastructure services
        self.error_manager = ErrorManager()
        self.memory = MemoryService()
        
        self.orchestrator_id = "multi_agent_orchestrator"
        self.workflow = self._build_workflow()
    
    def _build_workflow(self):
        """Build multi-agent orchestration workflow"""
        workflow = StateGraph(MultiAgentWorkflowState)
        
        # Add nodes for each specialized agent
        workflow.add_node("market_research", self._execute_market_research)
        workflow.add_node("vendor_discovery", self._execute_vendor_discovery)
        workflow.add_node("document_generation", self._execute_document_generation)
        workflow.add_node("consolidate_results", self._consolidate_results)
        
        # Define parallel execution for market research and vendor discovery
        workflow.set_entry_point("market_research")
        workflow.add_edge("market_research", "vendor_discovery")
        workflow.add_edge("vendor_discovery", "document_generation")
        workflow.add_edge("document_generation", "consolidate_results")
        workflow.add_edge("consolidate_results", END)
        
        return workflow.compile()
    
    async def _execute_market_research(self, state: MultiAgentWorkflowState) -> MultiAgentWorkflowState:
        """Execute market research using specialized agent"""
        try:
            start_time = datetime.utcnow()
            
            logger.info(f"Starting market research for {state.category} in {state.region}")
            
            # Execute market research agent
            market_results = await self.market_research_agent.research_market(
                query=state.rfq_request,
                region=state.region,
                category=state.category,
                budget_range=state.budget_range,
                urgency=state.urgency
            )
            
            state.market_research_results = market_results
            state.agents_completed.append("market_research_agent")
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"Market research completed in {execution_time:.2f}s with quality score: {market_results.get('research_quality_score', 0)}")
            
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=state.workflow_id,
                step_name="market_research"
            )
            
            state.market_research_results = {
                "success": False,
                "error": error_context,
                "agent_id": "market_research_agent"
            }
            
            logger.error(f"Market research failed: {error_context}")
            return state
    
    async def _execute_vendor_discovery(self, state: MultiAgentWorkflowState) -> MultiAgentWorkflowState:
        """Execute vendor discovery using specialized agent"""
        try:
            start_time = datetime.utcnow()
            
            logger.info(f"Starting vendor discovery for {state.category} in {state.region}")
            
            # Execute vendor discovery agent
            vendor_results = await self.vendor_discovery_agent.discover_vendors(
                category=state.category,
                region=state.region,
                budget_range=state.budget_range
            )
            
            state.vendor_discovery_results = vendor_results
            state.agents_completed.append("vendor_discovery_agent")
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"Vendor discovery completed in {execution_time:.2f}s with {vendor_results.get('total_vendors', 0)} vendors found")
            
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=state.workflow_id,
                step_name="vendor_discovery"
            )
            
            state.vendor_discovery_results = {
                "success": False,
                "error": error_context,
                "agent_id": "vendor_discovery_agent"
            }
            
            logger.error(f"Vendor discovery failed: {error_context}")
            return state
    
    async def _execute_document_generation(self, state: MultiAgentWorkflowState) -> MultiAgentWorkflowState:
        """Execute document generation using specialized agent"""
        try:
            start_time = datetime.utcnow()
            
            logger.info(f"Starting document generation for {state.category}")
            
            # Prepare document requirements from previous agent results
            document_requirements = DocumentRequirements(
                rfq_type="standard",
                category=state.category,
                region=state.region,
                budget_range=state.budget_range,
                urgency=state.urgency,
                department=state.department,
                compliance_requirements=self._extract_compliance_requirements(state),
                evaluation_criteria=self._extract_evaluation_criteria(state),
                custom_specifications=self._extract_custom_specifications(state)
            )
            
            # Execute document generation agent
            document_results = await self.document_generation_agent.generate_rfq_document(
                requirements=document_requirements
            )
            
            state.document_generation_results = document_results
            state.agents_completed.append("document_generation_agent")
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"Document generation completed in {execution_time:.2f}s with quality score: {document_results.get('quality_score', 0)}")
            
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=state.workflow_id,
                step_name="document_generation"
            )
            
            state.document_generation_results = {
                "success": False,
                "error": error_context,
                "agent_id": "document_generation_agent"
            }
            
            logger.error(f"Document generation failed: {error_context}")
            return state
    
    async def _consolidate_results(self, state: MultiAgentWorkflowState) -> MultiAgentWorkflowState:
        """Consolidate results from all specialized agents"""
        try:
            logger.info("Consolidating multi-agent results")
            
            # Calculate overall quality score
            quality_scores = []
            
            if state.market_research_results and state.market_research_results.get("success"):
                quality_scores.append(state.market_research_results.get("research_quality_score", 0))
            
            if state.vendor_discovery_results and state.vendor_discovery_results.get("success"):
                quality_scores.append(state.vendor_discovery_results.get("quality_score", 0))
            
            if state.document_generation_results and state.document_generation_results.get("success"):
                quality_scores.append(state.document_generation_results.get("quality_score", 0))
            
            if quality_scores:
                state.overall_quality_score = sum(quality_scores) / len(quality_scores)
            else:
                state.overall_quality_score = 0.0
            
            # Store consolidated results
            consolidated_results = {
                "workflow_id": state.workflow_id,
                "rfq_request": state.rfq_request,
                "category": state.category,
                "region": state.region,
                "agents_completed": state.agents_completed,
                "market_research": state.market_research_results,
                "vendor_discovery": state.vendor_discovery_results,
                "document_generation": state.document_generation_results,
                "overall_quality_score": state.overall_quality_score,
                "execution_timestamp": datetime.utcnow().isoformat()
            }
            
            await self.memory.store_conversation_memory(
                agent_id=self.orchestrator_id,
                content=f"Multi-agent workflow results: {json.dumps(consolidated_results, default=str)}",
                memory_type="multi_agent_results",
                metadata={
                    "workflow_id": state.workflow_id,
                    "category": state.category,
                    "quality_score": state.overall_quality_score
                }
            )
            
            logger.info(f"Consolidated results with overall quality score: {state.overall_quality_score}")
            
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=state.workflow_id,
                step_name="consolidate_results"
            )
            
            logger.error(f"Result consolidation failed: {error_context}")
            return state
    
    def _extract_compliance_requirements(self, state: MultiAgentWorkflowState) -> List[str]:
        """Extract compliance requirements from market research"""
        if (state.market_research_results and 
            state.market_research_results.get("regulatory_requirements")):
            return ["Standard compliance requirements"]
        return []
    
    def _extract_evaluation_criteria(self, state: MultiAgentWorkflowState) -> List[str]:
        """Extract evaluation criteria from market research"""
        return [
            "Price competitiveness (40%)",
            "Technical compliance (30%)",
            "Delivery capability (20%)",
            "Vendor reputation (10%)"
        ]
    
    def _extract_custom_specifications(self, state: MultiAgentWorkflowState) -> List[str]:
        """Extract custom specifications from market research"""
        if (state.market_research_results and 
            state.market_research_results.get("price_analysis")):
            return ["Specifications based on market analysis"]
        return []
    
    async def process_rfq_request(self, rfq_request: str, region: str, category: str, 
                                budget_range: Optional[str] = None, urgency: str = "medium",
                                department: str = "procurement") -> Dict[str, Any]:
        """
        Main entry point for multi-agent RFQ processing
        
        Coordinates all specialized agents to provide comprehensive RFQ workflow
        """
        try:
            start_time = datetime.utcnow()
            
            # Initialize workflow state
            state = MultiAgentWorkflowState(
                rfq_request=rfq_request,
                region=region,
                category=category,
                budget_range=budget_range,
                urgency=urgency,
                department=department
            )
            
            logger.info(f"Starting multi-agent workflow {state.workflow_id} for {category} procurement")
            
            # Execute multi-agent workflow
            final_state = await self.workflow.ainvoke(state)
            
            # Calculate total execution time
            total_time = (datetime.utcnow() - start_time).total_seconds()
            final_state.execution_time = total_time
            
            # Prepare comprehensive response
            response = {
                "success": True,
                "workflow_id": final_state.workflow_id,
                "rfq_request": rfq_request,
                "category": category,
                "region": region,
                "agents_executed": final_state.agents_completed,
                "execution_time": total_time,
                "overall_quality_score": final_state.overall_quality_score,
                
                # Individual agent results
                "market_research": final_state.market_research_results,
                "vendor_discovery": final_state.vendor_discovery_results,
                "document_generation": final_state.document_generation_results,
                
                # Summary
                "summary": {
                    "total_vendors_found": (
                        final_state.vendor_discovery_results.get("total_vendors", 0)
                        if final_state.vendor_discovery_results else 0
                    ),
                    "document_generated": bool(
                        final_state.document_generation_results and 
                        final_state.document_generation_results.get("success")
                    ),
                    "market_analysis_completed": bool(
                        final_state.market_research_results and 
                        final_state.market_research_results.get("success")
                    )
                },
                
                # Next steps
                "recommended_next_steps": self._generate_next_steps(final_state),
                
                "orchestrator_id": self.orchestrator_id,
                "factor_compliance": ["Factor 10: Small, Focused Agents"]
            }
            
            logger.info(f"Multi-agent workflow completed in {total_time:.2f}s with quality score: {final_state.overall_quality_score}")
            
            return response
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={
                    "rfq_request": rfq_request,
                    "category": category,
                    "region": region
                },
                execution_id=f"multi_agent_{datetime.utcnow().timestamp()}",
                step_name="process_rfq_request"
            )
            
            return {
                "success": False,
                "error": error_context,
                "orchestrator_id": self.orchestrator_id
            }
    
    def _generate_next_steps(self, state: MultiAgentWorkflowState) -> List[str]:
        """Generate recommended next steps based on agent results"""
        next_steps = []
        
        if state.document_generation_results and state.document_generation_results.get("success"):
            next_steps.append("Review and approve generated RFQ document")
        
        if state.vendor_discovery_results and state.vendor_discovery_results.get("total_vendors", 0) > 0:
            next_steps.append("Contact recommended vendors with RFQ")
            next_steps.append("Schedule vendor evaluation meetings")
        
        if state.market_research_results and state.market_research_results.get("success"):
            next_steps.append("Review market intelligence for procurement strategy")
        
        next_steps.extend([
            "Set up vendor response tracking system",
            "Prepare evaluation criteria and scoring matrix",
            "Schedule stakeholder review meeting"
        ])
        
        return next_steps
    
    def get_orchestrator_capabilities(self) -> Dict[str, Any]:
        """Return orchestrator capabilities and agent information"""
        return {
            "orchestrator_id": self.orchestrator_id,
            "orchestrator_type": "multi_agent",
            "managed_agents": [
                self.market_research_agent.get_agent_capabilities(),
                self.vendor_discovery_agent.get_agent_capabilities(),
                self.document_generation_agent.get_agent_capabilities()
            ],
            "capabilities": [
                "comprehensive_rfq_processing",
                "multi_agent_coordination", 
                "parallel_agent_execution",
                "result_consolidation"
            ],
            "description": "Orchestrates multiple specialized agents for comprehensive RFQ processing",
            "version": "1.0.0",
            "factor_compliance": ["Factor 10: Small, Focused Agents"],
            "workflow_steps": [
                "market_research",
                "vendor_discovery", 
                "document_generation",
                "result_consolidation"
            ]
        }