# requirements/development.txt
# Development dependencies including production requirements

-r production.txt

# ========== TESTING ==========
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-xdist>=3.3.1
httpx>=0.25.0
factory-boy>=3.3.0

# ========== CODE QUALITY ==========
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.6.0
pylint>=3.0.0
bandit>=1.7.5

# ========== DEVELOPMENT TOOLS ==========
pre-commit>=3.5.0
ipython>=8.17.0
jupyter>=1.0.0
notebook>=7.0.0

# ========== DEBUGGING ==========
pdb++>=0.10.3
debugpy>=1.8.0

# ========== TYPE CHECKING ==========
types-redis>=4.6.0
types-requests>=2.31.0