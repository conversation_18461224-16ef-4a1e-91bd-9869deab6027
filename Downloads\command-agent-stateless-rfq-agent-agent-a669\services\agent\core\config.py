"""
Core configuration module implementing 12-Factor App principles
Centralized settings management with environment-based configuration
"""

from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import Optional, List
import os
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings following 12-Factor App principles"""
    
    # ========== APPLICATION SETTINGS ==========
    app_name: str = Field(default="LangGraph AI Agent", env="APP_NAME")
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # ========== DATABASE CONFIGURATION ==========
    database_url: str = Field(env="DATABASE_URL")
    database_pool_size: int = Field(default=20, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    
    # ========== REDIS CONFIGURATION ==========
    redis_url: str = Field(env="REDIS_URL")
    redis_max_connections: int = Field(default=100, env="REDIS_MAX_CONNECTIONS")
    
    # ========== API KEYS ==========
    perplexity_api_key: str = Field(env="PERPLEXITY_API_KEY")
    
    # ========== SECURITY ==========
    secret_key: str = Field(env="SECRET_KEY", default="development-key-change-in-production")
    jwt_secret: str = Field(env="JWT_SECRET", default="jwt-secret-change-in-production")
    encryption_key: str = Field(env="ENCRYPTION_KEY", default="encryption-key-change")
    
    # ========== CORS SETTINGS ==========
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        env="ALLOWED_ORIGINS"
    )
    
    # ========== RATE LIMITING ==========
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, env="RATE_LIMIT_WINDOW")
    
    # ========== AGENT CONFIGURATION ==========
    max_concurrent_workflows: int = Field(default=50, env="MAX_CONCURRENT_WORKFLOWS")
    workflow_timeout: int = Field(default=3600, env="WORKFLOW_TIMEOUT")  # 1 hour
    context_window_size: int = Field(default=32000, env="CONTEXT_WINDOW_SIZE")
    
    # ========== MEMORY SYSTEM ==========
    embedding_batch_size: int = Field(default=32, env="EMBEDDING_BATCH_SIZE")
    vector_dimension: int = Field(default=1536, env="VECTOR_DIMENSION")
    memory_consolidation_interval: int = Field(default=3600, env="MEMORY_CONSOLIDATION_INTERVAL")
    
    # ========== PERPLEXITY CONFIGURATION ==========
    perplexity_model: str = Field(default="sonar-pro", env="PERPLEXITY_MODEL")
    perplexity_max_tokens: int = Field(default=4000, env="PERPLEXITY_MAX_TOKENS")
    perplexity_temperature: float = Field(default=0.1, env="PERPLEXITY_TEMPERATURE")
    
    # ========== MONITORING ==========
    enable_monitoring: bool = Field(default=True, env="ENABLE_MONITORING")
    metrics_port: int = Field(default=8001, env="METRICS_PORT")
    
    # ========== FEATURE FLAGS ==========
    enable_human_in_loop: bool = Field(default=True, env="ENABLE_HUMAN_IN_LOOP")
    enable_advanced_memory: bool = Field(default=True, env="ENABLE_ADVANCED_MEMORY")
    enable_async_processing: bool = Field(default=True, env="ENABLE_ASYNC_PROCESSING")
    
    # ========== CELERY CONFIGURATION ==========
    celery_broker_url: str = Field(env="RABBITMQ_URL", default="redis://localhost:6379/3")
    celery_result_backend: str = Field(env="REDIS_URL", default="redis://localhost:6379/3")
    celery_workers: int = Field(default=4, env="CELERY_WORKERS")
    
    # ========== TOOL CALLING ==========
    tool_call_timeout: int = Field(default=30, env="TOOL_CALL_TIMEOUT")
    max_tool_calls_per_request: int = Field(default=50, env="MAX_TOOL_CALLS_PER_REQUEST")
    
    # ========== ERROR HANDLING ==========
    max_retry_attempts: int = Field(default=3, env="MAX_RETRY_ATTEMPTS")
    retry_delay: float = Field(default=1.0, env="RETRY_DELAY")
    error_context_window: int = Field(default=1000, env="ERROR_CONTEXT_WINDOW")
    
    @validator("environment")
    def validate_environment(cls, v):
        valid_environments = ["development", "staging", "production"]
        if v not in valid_environments:
            raise ValueError(f"Environment must be one of {valid_environments}")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()
    
    @validator("database_url")
    def validate_database_url(cls, v):
        if not v.startswith("postgresql://"):
            raise ValueError("Database URL must be a PostgreSQL connection string")
        return v
    
    @validator("redis_url")
    def validate_redis_url(cls, v):
        if not v.startswith("redis://"):
            raise ValueError("Redis URL must be a valid Redis connection string")
        return v
    
    @property
    def is_production(self) -> bool:
        return self.environment == "production"
    
    @property
    def is_development(self) -> bool:
        return self.environment == "development"
    
    @property
    def database_config(self) -> dict:
        """Database configuration dictionary"""
        return {
            "url": self.database_url,
            "pool_size": self.database_pool_size,
            "max_overflow": self.database_max_overflow,
            "echo": self.is_development,
        }
    
    @property
    def redis_config(self) -> dict:
        """Redis configuration dictionary"""
        return {
            "url": self.redis_url,
            "max_connections": self.redis_max_connections,
            "decode_responses": True,
        }
    
    @property
    def perplexity_config(self) -> dict:
        """Perplexity API configuration"""
        return {
            "api_key": self.perplexity_api_key,
            "model": self.perplexity_model,
            "max_tokens": self.perplexity_max_tokens,
            "temperature": self.perplexity_temperature,
        }
    
    @property
    def celery_config(self) -> dict:
        """Celery configuration dictionary"""
        return {
            "broker_url": self.celery_broker_url,
            "result_backend": self.celery_result_backend,
            "task_serializer": "json",
            "accept_content": ["json"],
            "result_serializer": "json",
            "timezone": "UTC",
            "enable_utc": True,
            "worker_concurrency": self.celery_workers,
            "worker_prefetch_multiplier": 1,
            "task_acks_late": True,
            "worker_max_tasks_per_child": 1000,
        }

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()

# Global settings instance
settings = get_settings()