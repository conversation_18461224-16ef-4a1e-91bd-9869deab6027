"""
Memory Service for semantic memory management with pgvector
Implements Factor 3: Own Your Context Window with intelligent memory retrieval
"""

import structlog
import json
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import numpy as np

from ..core.database import memory_db, get_database
from ..core.redis_client import cache_manager
from .embedding_service import EmbeddingService

logger = structlog.get_logger(__name__)

class MemoryService:
    """Service for managing semantic memories with pgvector"""
    
    def __init__(self):
        self.embedding_service = EmbeddingService()
        self.cache_ttl = 3600  # 1 hour cache
    
    async def store_memory(
        self,
        content: str,
        memory_type: str,
        metadata: Dict[str, Any],
        importance_score: float = 0.5,
        source: str = "workflow"
    ) -> str:
        """
        Store a memory with semantic embedding
        Implements Factor 3: Context Window Management
        """
        logger.info("Storing memory", memory_type=memory_type, content_length=len(content))
        
        try:
            # Generate embedding for the content
            embedding = await self.embedding_service.generate_embedding(content)
            
            # Store in database
            memory_id = await memory_db.store_memory(
                content=content,
                embedding=embedding,
                memory_type=memory_type,
                metadata={
                    **metadata,
                    "source": source,
                    "created_by": "memory_service",
                    "content_length": len(content)
                },
                importance_score=importance_score
            )
            
            # Invalidate related cache
            await cache_manager.invalidate_pattern(f"memory:search:*{memory_type}*")
            
            logger.info("Memory stored successfully", memory_id=memory_id, memory_type=memory_type)
            return str(memory_id)
            
        except Exception as e:
            logger.error("Failed to store memory", error=str(e), memory_type=memory_type)
            raise
    
    async def retrieve_context(
        self,
        query: str,
        limit: int = 10,
        memory_types: List[str] = None,
        importance_threshold: float = 0.3
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant memories for context
        Implements semantic search with caching
        """
        logger.info("Retrieving memory context", query=query[:100], limit=limit)
        
        try:
            # Generate cache key
            cache_key = f"memory:search:{hash(query)}:{limit}:{importance_threshold}"
            if memory_types:
                cache_key += f":{'_'.join(sorted(memory_types))}"
            
            # Check cache first
            cached_result = await cache_manager.redis.get(cache_key)
            if cached_result:
                logger.debug("Memory context cache hit", query=query[:50])
                return cached_result
            
            # Generate query embedding
            query_embedding = await self.embedding_service.generate_embedding(query)
            
            # Search memories
            memories = []
            if memory_types:
                for memory_type in memory_types:
                    type_memories = await memory_db.search_memories(
                        query_embedding=query_embedding,
                        limit=limit // len(memory_types) + 1,
                        memory_type=memory_type,
                        importance_threshold=importance_threshold
                    )
                    memories.extend(type_memories)
            else:
                memories = await memory_db.search_memories(
                    query_embedding=query_embedding,
                    limit=limit,
                    importance_threshold=importance_threshold
                )
            
            # Sort by similarity and limit results
            memories.sort(key=lambda x: x.get('similarity', 0), reverse=True)
            memories = memories[:limit]
            
            # Update access tracking
            for memory in memories:
                await memory_db.update_memory_access(memory['id'])
            
            # Convert to context format
            context = []
            for memory in memories:
                context.append({
                    "id": str(memory['id']),
                    "content": memory['content'],
                    "memory_type": memory['memory_type'],
                    "metadata": memory['metadata'],
                    "similarity": memory.get('similarity', 0),
                    "importance_score": memory.get('importance_score', 0),
                    "created_at": memory['created_at'].isoformat() if memory.get('created_at') else None
                })
            
            # Cache the result
            await cache_manager.redis.set(cache_key, context, ex=self.cache_ttl)
            
            logger.info("Memory context retrieved", 
                       query=query[:50], 
                       results_count=len(context))
            
            return context
            
        except Exception as e:
            logger.error("Failed to retrieve memory context", error=str(e), query=query[:100])
            return []
    
    async def search_memories(
        self,
        query: str,
        limit: int = 10,
        memory_type: str = None,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search memories with high similarity threshold
        """
        logger.info("Searching memories", query=query[:100], memory_type=memory_type)
        
        try:
            # Generate query embedding
            query_embedding = await self.embedding_service.generate_embedding(query)
            
            # Search memories
            memories = await memory_db.search_memories(
                query_embedding=query_embedding,
                limit=limit,
                memory_type=memory_type,
                importance_threshold=similarity_threshold
            )
            
            # Format results
            results = []
            for memory in memories:
                if memory.get('similarity', 0) >= similarity_threshold:
                    results.append({
                        "id": str(memory['id']),
                        "content": memory['content'],
                        "memory_type": memory['memory_type'],
                        "metadata": memory['metadata'],
                        "similarity": memory.get('similarity', 0),
                        "importance_score": memory.get('importance_score', 0),
                        "last_accessed": memory.get('last_accessed'),
                        "created_at": memory['created_at'].isoformat() if memory.get('created_at') else None
                    })
            
            logger.info("Memory search completed", 
                       query=query[:50], 
                       results_count=len(results))
            
            return results
            
        except Exception as e:
            logger.error("Memory search failed", error=str(e), query=query[:100])
            return []
    
    async def consolidate_memories(
        self,
        execution_id: str = None,
        batch_size: int = 100
    ) -> int:
        """
        Consolidate memories to improve retrieval performance
        Implements memory optimization for Factor 3
        """
        logger.info("Starting memory consolidation", 
                   execution_id=execution_id, 
                   batch_size=batch_size)
        
        try:
            # Consolidate memories in database
            consolidated_count = await memory_db.consolidate_memories(batch_size)
            
            # Clear related caches to force refresh
            await cache_manager.invalidate_pattern("memory:search:*")
            
            logger.info("Memory consolidation completed", 
                       consolidated_count=consolidated_count,
                       execution_id=execution_id)
            
            return consolidated_count
            
        except Exception as e:
            logger.error("Memory consolidation failed", 
                        error=str(e), 
                        execution_id=execution_id)
            return 0
    
    async def store_conversation_memory(
        self,
        user_id: str,
        conversation_data: Dict[str, Any],
        summary: str = None
    ) -> str:
        """Store conversation as searchable memory"""
        logger.info("Storing conversation memory", user_id=user_id)
        
        try:
            # Generate summary if not provided
            if not summary:
                summary = self._generate_conversation_summary(conversation_data)
            
            # Store as memory
            memory_id = await self.store_memory(
                content=summary,
                memory_type="conversation",
                metadata={
                    "user_id": user_id,
                    "conversation_data": conversation_data,
                    "message_count": len(conversation_data.get("messages", [])),
                    "conversation_type": "rfq_workflow"
                },
                importance_score=0.6
            )
            
            # Also store in conversations table
            db = await get_database()
            conversation_embedding = await self.embedding_service.generate_embedding(summary)
            
            async with db.acquire() as connection:
                query = """
                INSERT INTO agent_memory.conversations 
                (user_id, conversation_data, summary, embedding, message_count)
                VALUES ($1, $2, $3, $4::vector, $5)
                RETURNING id
                """
                
                conversation_id = await connection.fetchval(
                    query,
                    user_id,
                    conversation_data,
                    summary,
                    conversation_embedding,
                    len(conversation_data.get("messages", []))
                )
            
            logger.info("Conversation memory stored", 
                       memory_id=memory_id,
                       conversation_id=conversation_id)
            
            return str(memory_id)
            
        except Exception as e:
            logger.error("Failed to store conversation memory", 
                        error=str(e), 
                        user_id=user_id)
            raise
    
    async def get_user_conversation_history(
        self,
        user_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get user's conversation history"""
        logger.info("Retrieving conversation history", user_id=user_id, limit=limit)
        
        try:
            db = await get_database()
            
            async with db.acquire() as connection:
                query = """
                SELECT id, conversation_data, summary, message_count, created_at, updated_at
                FROM agent_memory.conversations
                WHERE user_id = $1
                ORDER BY updated_at DESC
                LIMIT $2
                """
                
                rows = await connection.fetch(query, user_id, limit)
                
                conversations = []
                for row in rows:
                    conversations.append({
                        "id": str(row['id']),
                        "conversation_data": row['conversation_data'],
                        "summary": row['summary'],
                        "message_count": row['message_count'],
                        "created_at": row['created_at'].isoformat(),
                        "updated_at": row['updated_at'].isoformat()
                    })
                
                logger.info("Conversation history retrieved", 
                           user_id=user_id, 
                           count=len(conversations))
                
                return conversations
            
        except Exception as e:
            logger.error("Failed to retrieve conversation history", 
                        error=str(e), 
                        user_id=user_id)
            return []
    
    async def store_workflow_memory(
        self,
        workflow_data: Dict[str, Any],
        execution_id: str,
        user_id: str
    ) -> str:
        """Store workflow execution as memory for future reference"""
        logger.info("Storing workflow memory", execution_id=execution_id)
        
        try:
            # Generate workflow summary
            summary = self._generate_workflow_summary(workflow_data)
            
            # Store workflow memory
            memory_id = await self.store_memory(
                content=summary,
                memory_type="workflow_execution",
                metadata={
                    "execution_id": execution_id,
                    "user_id": user_id,
                    "workflow_type": "rfq",
                    "success": workflow_data.get("success", False),
                    "vendor_count": len(workflow_data.get("vendors", [])),
                    "department": workflow_data.get("parsed_request", {}).get("department"),
                    "urgency": workflow_data.get("parsed_request", {}).get("urgency")
                },
                importance_score=0.7 if workflow_data.get("success") else 0.4
            )
            
            logger.info("Workflow memory stored", memory_id=memory_id)
            return memory_id
            
        except Exception as e:
            logger.error("Failed to store workflow memory", 
                        error=str(e), 
                        execution_id=execution_id)
            raise
    
    def _generate_conversation_summary(self, conversation_data: Dict[str, Any]) -> str:
        """Generate summary of conversation data"""
        messages = conversation_data.get("messages", [])
        if not messages:
            return "Empty conversation"
        
        # Extract key information
        user_messages = [msg for msg in messages if msg.get("role") == "user"]
        system_messages = [msg for msg in messages if msg.get("role") == "system"]
        
        if user_messages:
            first_user_message = user_messages[0].get("content", "")
            summary = f"User conversation started with: {first_user_message[:200]}"
            
            if len(user_messages) > 1:
                summary += f" Total user messages: {len(user_messages)}"
        else:
            summary = f"System conversation with {len(system_messages)} messages"
        
        return summary
    
    def _generate_workflow_summary(self, workflow_data: Dict[str, Any]) -> str:
        """Generate summary of workflow execution"""
        try:
            request = workflow_data.get("parsed_request", {})
            vendors = workflow_data.get("vendors", [])
            success = workflow_data.get("success", False)
            
            summary = f"RFQ workflow for {request.get('department', 'Unknown')} department. "
            summary += f"Request: {request.get('natural_language_request', '')[:200]}. "
            summary += f"Found {len(vendors)} vendors. "
            summary += f"Status: {'Completed successfully' if success else 'Failed or incomplete'}."
            
            if request.get("urgency"):
                summary += f" Urgency: {request['urgency']}."
            
            if request.get("budget_range"):
                summary += f" Budget: {request['budget_range']}."
            
            return summary
            
        except Exception as e:
            logger.error("Failed to generate workflow summary", error=str(e))
            return "Workflow execution summary unavailable"
    
    async def get_memory_statistics(self) -> Dict[str, Any]:
        """Get memory system statistics"""
        try:
            db = await get_database()
            
            async with db.acquire() as connection:
                # Get memory counts by type
                type_counts_query = """
                SELECT memory_type, COUNT(*) as count, AVG(importance_score) as avg_importance
                FROM agent_memory.memories
                GROUP BY memory_type
                ORDER BY count DESC
                """
                
                type_counts = await connection.fetch(type_counts_query)
                
                # Get total memory count
                total_count_query = "SELECT COUNT(*) FROM agent_memory.memories"
                total_count = await connection.fetchval(total_count_query)
                
                # Get recent activity
                recent_activity_query = """
                SELECT COUNT(*) FROM agent_memory.memories
                WHERE created_at > NOW() - INTERVAL '24 hours'
                """
                recent_count = await connection.fetchval(recent_activity_query)
                
                # Get cluster information
                cluster_count_query = "SELECT COUNT(*) FROM agent_memory.memory_clusters"
                cluster_count = await connection.fetchval(cluster_count_query)
                
                statistics = {
                    "total_memories": total_count,
                    "recent_memories_24h": recent_count,
                    "memory_clusters": cluster_count,
                    "memory_types": [
                        {
                            "type": row["memory_type"],
                            "count": row["count"],
                            "avg_importance": float(row["avg_importance"]) if row["avg_importance"] else 0
                        }
                        for row in type_counts
                    ],
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                return statistics
            
        except Exception as e:
            logger.error("Failed to get memory statistics", error=str(e))
            return {
                "total_memories": 0,
                "recent_memories_24h": 0,
                "memory_clusters": 0,
                "memory_types": [],
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }