"""
Factor 10: Small, Focused Agents - Document Generation Agent
Specialized agent for RFQ document creation and customization
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import logging
from langgraph import StateGraph
from langgraph.graph import END

from ..services.perplexity_service import PerplexityService
from ..services.memory_service import MemoryService
from ..services.error_manager import ErrorManager

logger = logging.getLogger(__name__)

@dataclass
class DocumentRequirements:
    """Requirements for document generation"""
    rfq_type: str
    category: str
    region: str
    budget_range: Optional[str] = None
    urgency: str = "medium"
    department: str = "procurement"
    compliance_requirements: List[str] = field(default_factory=list)
    evaluation_criteria: List[str] = field(default_factory=list)
    custom_specifications: List[str] = field(default_factory=list)

@dataclass
class RFQDocument:
    """Generated RFQ document structure"""
    document_id: str
    title: str
    description: str
    specifications: List[str]
    evaluation_criteria: List[str]
    submission_requirements: List[str]
    delivery_requirements: str
    deadline: str
    legal_terms: List[str]
    compliance_notes: List[str]
    contact_information: Dict[str, str]
    document_version: str = "1.0"
    created_at: datetime = field(default_factory=datetime.utcnow)

@dataclass
class DocumentGenerationState:
    """State for document generation workflow"""
    requirements: DocumentRequirements
    
    # Generated components
    document_structure: Optional[Dict[str, Any]] = None
    content_sections: Optional[Dict[str, str]] = None
    compliance_section: Optional[str] = None
    legal_terms: Optional[List[str]] = None
    final_document: Optional[RFQDocument] = None
    
    # Quality metrics
    document_quality_score: float = 0.0
    compliance_score: float = 0.0
    completeness_score: float = 0.0

class DocumentGenerationAgent:
    """Factor 10: Small, Focused Agent for Document Generation"""
    
    def __init__(self):
        self.perplexity = PerplexityService()
        self.memory = MemoryService()
        self.error_manager = ErrorManager()
        self.agent_id = "document_generation_agent"
        self.workflow = self._build_workflow()
    
    def _build_workflow(self):
        """Build document generation workflow"""
        workflow = StateGraph(DocumentGenerationState)
        
        workflow.add_node("analyze_requirements", self._analyze_requirements)
        workflow.add_node("generate_structure", self._generate_document_structure)
        workflow.add_node("create_content", self._create_content_sections)
        workflow.add_node("add_compliance", self._add_compliance_requirements)
        workflow.add_node("finalize_document", self._finalize_document)
        
        workflow.set_entry_point("analyze_requirements")
        workflow.add_edge("analyze_requirements", "generate_structure")
        workflow.add_edge("generate_structure", "create_content")
        workflow.add_edge("create_content", "add_compliance")
        workflow.add_edge("add_compliance", "finalize_document")
        workflow.add_edge("finalize_document", END)
        
        return workflow.compile()
    
    async def _analyze_requirements(self, state: DocumentGenerationState) -> DocumentGenerationState:
        """Analyze and enhance document requirements"""
        try:
            # Use Perplexity to research best practices for RFQ documents
            requirements_query = f"""
            Research best practices for RFQ documents in {state.requirements.category} procurement.
            
            Include:
            1. Essential sections and structure for professional RFQs
            2. Industry-specific requirements and specifications
            3. Standard evaluation criteria for {state.requirements.category}
            4. Compliance requirements for {state.requirements.region}
            5. Legal terms and conditions best practices
            6. Submission format and deadline considerations
            
            Focus on creating comprehensive, professional procurement documents.
            """
            
            requirements_research = await self.perplexity.research_with_sources(
                query=requirements_query,
                context="rfq_document_best_practices"
            )
            
            # Store requirements analysis
            await self.memory.store_conversation_memory(
                agent_id=self.agent_id,
                content=f"RFQ requirements analysis: {requirements_research['content'][:500]}...",
                memory_type="document_requirements",
                metadata={
                    "category": state.requirements.category,
                    "region": state.requirements.region
                }
            )
            
            logger.info(f"Analyzed requirements for {state.requirements.category} RFQ")
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=f"document_gen_{datetime.utcnow().timestamp()}",
                step_name="analyze_requirements"
            )
            logger.error(f"Requirements analysis failed: {error_context}")
            return state
    
    async def _generate_document_structure(self, state: DocumentGenerationState) -> DocumentGenerationState:
        """Generate optimal document structure"""
        try:
            # Create document structure based on requirements
            document_structure = {
                "header": {
                    "title": f"Request for Quotation - {state.requirements.category}",
                    "rfq_number": f"RFQ-{datetime.utcnow().strftime('%Y%m%d')}-{state.requirements.category[:3].upper()}",
                    "issue_date": datetime.utcnow().strftime("%Y-%m-%d"),
                    "deadline": (datetime.utcnow() + timedelta(days=14)).strftime("%Y-%m-%d")
                },
                "sections": [
                    "executive_summary",
                    "project_overview", 
                    "technical_specifications",
                    "commercial_requirements",
                    "evaluation_criteria",
                    "submission_requirements",
                    "terms_and_conditions",
                    "contact_information"
                ],
                "appendices": [
                    "compliance_checklist",
                    "reference_documents",
                    "submission_templates"
                ]
            }
            
            state.document_structure = document_structure
            
            logger.info("Generated document structure")
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=f"document_gen_{datetime.utcnow().timestamp()}",
                step_name="generate_structure"
            )
            logger.error(f"Structure generation failed: {error_context}")
            return state
    
    async def _create_content_sections(self, state: DocumentGenerationState) -> DocumentGenerationState:
        """Create content for each document section"""
        try:
            # Generate content using Perplexity for professional language
            content_query = f"""
            Generate professional RFQ content for {state.requirements.category} procurement.
            
            Create detailed content for:
            1. Executive Summary - Brief overview of procurement needs
            2. Project Overview - Detailed description of requirements
            3. Technical Specifications - Specific technical requirements
            4. Commercial Requirements - Pricing, payment, delivery terms
            5. Evaluation Criteria - How proposals will be evaluated
            6. Submission Requirements - Format, deadline, contact details
            
            Use professional procurement language suitable for {state.requirements.region}.
            Budget context: {state.requirements.budget_range or 'To be determined'}
            Urgency: {state.requirements.urgency}
            """
            
            content_research = await self.perplexity.generate_response(
                prompt=content_query,
                context="rfq_content_generation"
            )
            
            # Structure the generated content
            content_sections = {
                "executive_summary": self._extract_executive_summary(content_research),
                "project_overview": self._extract_project_overview(content_research, state.requirements),
                "technical_specifications": self._generate_technical_specs(state.requirements),
                "commercial_requirements": self._generate_commercial_terms(state.requirements),
                "evaluation_criteria": self._generate_evaluation_criteria(state.requirements),
                "submission_requirements": self._generate_submission_requirements(state.requirements)
            }
            
            state.content_sections = content_sections
            
            logger.info("Generated content sections")
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=f"document_gen_{datetime.utcnow().timestamp()}",
                step_name="create_content"
            )
            logger.error(f"Content creation failed: {error_context}")
            return state
    
    async def _add_compliance_requirements(self, state: DocumentGenerationState) -> DocumentGenerationState:
        """Add compliance and legal requirements"""
        try:
            # Research compliance requirements
            compliance_query = f"""
            Research compliance requirements for {state.requirements.category} procurement in {state.requirements.region}.
            
            Include:
            1. Legal and regulatory compliance requirements
            2. Industry-specific certifications and standards
            3. Environmental and sustainability requirements
            4. Quality assurance and testing requirements
            5. Documentation and reporting obligations
            6. Insurance and liability requirements
            
            Provide specific compliance language for RFQ documents.
            """
            
            compliance_research = await self.perplexity.research_with_sources(
                query=compliance_query,
                context="compliance_requirements"
            )
            
            # Generate compliance section
            compliance_section = self._generate_compliance_section(
                compliance_research["content"], 
                state.requirements
            )
            
            # Generate legal terms
            legal_terms = self._generate_legal_terms(state.requirements)
            
            state.compliance_section = compliance_section
            state.legal_terms = legal_terms
            
            # Calculate compliance score
            state.compliance_score = self._calculate_compliance_score(
                state.requirements.compliance_requirements,
                compliance_section
            )
            
            logger.info(f"Added compliance requirements with score: {state.compliance_score}")
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=f"document_gen_{datetime.utcnow().timestamp()}",
                step_name="add_compliance"
            )
            logger.error(f"Compliance addition failed: {error_context}")
            return state
    
    async def _finalize_document(self, state: DocumentGenerationState) -> DocumentGenerationState:
        """Finalize the complete RFQ document"""
        try:
            # Create final document
            document_id = f"RFQ-{datetime.utcnow().strftime('%Y%m%d%H%M')}-{state.requirements.category[:3].upper()}"
            
            final_document = RFQDocument(
                document_id=document_id,
                title=state.document_structure["header"]["title"],
                description=state.content_sections.get("project_overview", ""),
                specifications=self._compile_specifications(state),
                evaluation_criteria=self._compile_evaluation_criteria(state),
                submission_requirements=self._compile_submission_requirements(state),
                delivery_requirements=self._generate_delivery_requirements(state.requirements),
                deadline=state.document_structure["header"]["deadline"],
                legal_terms=state.legal_terms or [],
                compliance_notes=[state.compliance_section] if state.compliance_section else [],
                contact_information=self._generate_contact_info(state.requirements)
            )
            
            # Calculate quality scores
            state.completeness_score = self._calculate_completeness_score(final_document)
            state.document_quality_score = (
                state.compliance_score * 0.4 + 
                state.completeness_score * 0.6
            )
            
            state.final_document = final_document
            
            # Store final document
            await self.memory.store_conversation_memory(
                agent_id=self.agent_id,
                content=f"Generated RFQ document: {json.dumps(final_document.__dict__, default=str)}",
                memory_type="generated_documents",
                metadata={
                    "document_id": document_id,
                    "category": state.requirements.category,
                    "quality_score": state.document_quality_score
                }
            )
            
            logger.info(f"Finalized document {document_id} with quality score: {state.document_quality_score}")
            return state
            
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"state": state.__dict__},
                execution_id=f"document_gen_{datetime.utcnow().timestamp()}",
                step_name="finalize_document"
            )
            logger.error(f"Document finalization failed: {error_context}")
            return state
    
    # Helper methods
    def _extract_executive_summary(self, content: str) -> str:
        """Extract executive summary from generated content"""
        return "This RFQ outlines requirements for procurement of specified goods/services."
    
    def _extract_project_overview(self, content: str, requirements: DocumentRequirements) -> str:
        """Extract project overview from content"""
        return f"Procurement project for {requirements.category} with {requirements.urgency} priority."
    
    def _generate_technical_specs(self, requirements: DocumentRequirements) -> List[str]:
        """Generate technical specifications"""
        base_specs = [
            "Items must meet all specified quality standards",
            "Compliance with applicable industry regulations",
            "Warranty and support as per requirements"
        ]
        return base_specs + requirements.custom_specifications
    
    def _generate_commercial_terms(self, requirements: DocumentRequirements) -> str:
        """Generate commercial terms"""
        terms = f"Budget range: {requirements.budget_range or 'To be determined'}. "
        terms += f"Payment terms: Net 30 days. "
        terms += f"Delivery required in {requirements.region}."
        return terms
    
    def _generate_evaluation_criteria(self, requirements: DocumentRequirements) -> List[str]:
        """Generate evaluation criteria"""
        default_criteria = [
            "Price competitiveness (40%)",
            "Technical compliance (30%)",
            "Delivery capability (20%)",
            "Vendor experience (10%)"
        ]
        return requirements.evaluation_criteria or default_criteria
    
    def _generate_submission_requirements(self, requirements: DocumentRequirements) -> List[str]:
        """Generate submission requirements"""
        return [
            "Complete technical proposal",
            "Commercial pricing proposal", 
            "Company credentials and references",
            "Compliance documentation",
            "Signed terms and conditions"
        ]
    
    def _generate_compliance_section(self, research_content: str, requirements: DocumentRequirements) -> str:
        """Generate compliance section"""
        return f"All suppliers must comply with applicable regulations in {requirements.region}."
    
    def _generate_legal_terms(self, requirements: DocumentRequirements) -> List[str]:
        """Generate legal terms"""
        return [
            "Standard terms and conditions apply",
            "Confidentiality agreement required",
            "Liability limitations as per contract",
            "Governing law and jurisdiction clauses"
        ]
    
    def _compile_specifications(self, state: DocumentGenerationState) -> List[str]:
        """Compile all specifications"""
        return self._generate_technical_specs(state.requirements)
    
    def _compile_evaluation_criteria(self, state: DocumentGenerationState) -> List[str]:
        """Compile evaluation criteria"""
        return self._generate_evaluation_criteria(state.requirements)
    
    def _compile_submission_requirements(self, state: DocumentGenerationState) -> List[str]:
        """Compile submission requirements"""
        return self._generate_submission_requirements(state.requirements)
    
    def _generate_delivery_requirements(self, requirements: DocumentRequirements) -> str:
        """Generate delivery requirements"""
        return f"Delivery to {requirements.region} within agreed timeframe."
    
    def _generate_contact_info(self, requirements: DocumentRequirements) -> Dict[str, str]:
        """Generate contact information"""
        return {
            "department": requirements.department,
            "email": f"{requirements.department}@company.com",
            "phone": "******-0123"
        }
    
    def _calculate_compliance_score(self, required_compliance: List[str], section: str) -> float:
        """Calculate compliance score"""
        if not required_compliance:
            return 1.0
        return 0.8  # Mock score
    
    def _calculate_completeness_score(self, document: RFQDocument) -> float:
        """Calculate document completeness score"""
        required_fields = 8
        completed_fields = sum([
            bool(document.title),
            bool(document.description),
            bool(document.specifications),
            bool(document.evaluation_criteria),
            bool(document.submission_requirements),
            bool(document.delivery_requirements),
            bool(document.deadline),
            bool(document.contact_information)
        ])
        return completed_fields / required_fields
    
    async def generate_rfq_document(self, requirements: DocumentRequirements) -> Dict[str, Any]:
        """Main entry point for document generation"""
        try:
            state = DocumentGenerationState(requirements=requirements)
            final_state = await self.workflow.ainvoke(state)
            
            if final_state.final_document:
                return {
                    "success": True,
                    "document": final_state.final_document.__dict__,
                    "quality_score": final_state.document_quality_score,
                    "compliance_score": final_state.compliance_score,
                    "completeness_score": final_state.completeness_score,
                    "agent_id": self.agent_id
                }
            else:
                return {
                    "success": False,
                    "error": "Document generation failed",
                    "agent_id": self.agent_id
                }
                
        except Exception as e:
            error_context = await self.error_manager.compact_error_for_context(
                error=e,
                context={"requirements": requirements.__dict__},
                execution_id=f"document_gen_{datetime.utcnow().timestamp()}",
                step_name="generate_rfq_document"
            )
            
            return {
                "success": False,
                "error": error_context,
                "agent_id": self.agent_id
            }
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """Return agent capabilities"""
        return {
            "agent_id": self.agent_id,
            "agent_type": "document_generation",
            "capabilities": ["rfq_generation", "compliance_integration", "professional_formatting"],
            "description": "Specialized agent for RFQ document generation and customization",
            "version": "1.0.0",
            "factor_compliance": ["Factor 10: Small, Focused Agents"]
        }