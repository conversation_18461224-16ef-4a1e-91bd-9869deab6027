"""Monitoring and metrics setup."""
from fastapi import FastAPI
import time
from typing import Dict, Any

# Simple in-memory metrics store
_metrics = {
    "requests_total": 0,
    "requests_duration": [],
    "errors_total": 0,
    "uptime_start": time.time()
}

def setup_monitoring(app: FastAPI):
    """Setup monitoring for the application."""
    
    @app.middleware("http")
    async def metrics_middleware(request, call_next):
        start_time = time.time()
        
        try:
            response = await call_next(request)
            _metrics["requests_total"] += 1
            
            duration = time.time() - start_time
            _metrics["requests_duration"].append(duration)
            
            # Keep only last 1000 durations
            if len(_metrics["requests_duration"]) > 1000:
                _metrics["requests_duration"] = _metrics["requests_duration"][-1000:]
            
            return response
        except Exception as e:
            _metrics["errors_total"] += 1
            raise

def get_metrics() -> Dict[str, Any]:
    """Get current metrics."""
    uptime = time.time() - _metrics["uptime_start"]
    avg_duration = sum(_metrics["requests_duration"]) / len(_metrics["requests_duration"]) if _metrics["requests_duration"] else 0
    
    return {
        "requests_total": _metrics["requests_total"],
        "errors_total": _metrics["errors_total"],
        "uptime_seconds": uptime,
        "average_request_duration": avg_duration
    }
