"""
Factor 11: Input Gateway Service
Exposes multiple endpoints for universal input handling
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional, Union
import json
import logging
from datetime import datetime

from ..services.universal_input_handler import (
    UniversalInputHandler, 
    InputContext, 
    InputSource, 
    InputFormat
)

logger = logging.getLogger(__name__)

class TextInputRequest(BaseModel):
    """Text input via API"""
    text: str
    source: str = "api"
    priority: str = "medium"
    sender_info: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

class JSONInputRequest(BaseModel):
    """Structured JSON input"""
    data: Dict[str, Any]
    source: str = "api"
    priority: str = "medium"
    sender_info: Optional[Dict[str, Any]] = None

class EmailInputRequest(BaseModel):
    """Email input format"""
    subject: str
    body: str
    sender_email: str
    attachments: Optional[List[str]] = None
    priority: str = "medium"

class ChatInputRequest(BaseModel):
    """Chat input format"""
    message: str
    user_id: str
    channel_id: Optional[str] = None
    thread_id: Optional[str] = None

def create_input_gateway_routes(app: FastAPI, input_handler: UniversalInputHandler):
    """Create all input gateway routes"""
    
    @app.post("/api/input/text")
    async def handle_text_input(request: TextInputRequest):
        """Handle plain text input from any source"""
        try:
            context = InputContext(
                source=InputSource(request.source.lower()),
                format=InputFormat.TEXT,
                timestamp=datetime.utcnow(),
                sender_info=request.sender_info,
                metadata=request.metadata,
                priority=request.priority
            )
            
            result = await input_handler.process_input(request.text, context)
            return JSONResponse(content=result)
            
        except Exception as e:
            logger.error(f"Text input processing failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/input/json")
    async def handle_json_input(request: JSONInputRequest):
        """Handle structured JSON input"""
        try:
            context = InputContext(
                source=InputSource(request.source.lower()),
                format=InputFormat.JSON,
                timestamp=datetime.utcnow(),
                sender_info=request.sender_info,
                priority=request.priority
            )
            
            result = await input_handler.process_input(request.data, context)
            return JSONResponse(content=result)
            
        except Exception as e:
            logger.error(f"JSON input processing failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/input/email")
    async def handle_email_input(request: EmailInputRequest):
        """Handle email input"""
        try:
            email_data = {
                "subject": request.subject,
                "body": request.body,
                "sender": request.sender_email,
                "attachments": request.attachments or []
            }
            
            context = InputContext(
                source=InputSource.EMAIL,
                format=InputFormat.EMAIL_MSG,
                timestamp=datetime.utcnow(),
                sender_info={"email": request.sender_email},
                priority=request.priority
            )
            
            result = await input_handler.process_input(email_data, context)
            return JSONResponse(content=result)
            
        except Exception as e:
            logger.error(f"Email input processing failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/input/chat")
    async def handle_chat_input(request: ChatInputRequest):
        """Handle chat/messaging input"""
        try:
            context = InputContext(
                source=InputSource.CHAT,
                format=InputFormat.TEXT,
                timestamp=datetime.utcnow(),
                sender_info={
                    "user_id": request.user_id,
                    "channel_id": request.channel_id,
                    "thread_id": request.thread_id
                },
                priority="medium"
            )
            
            result = await input_handler.process_input(request.message, context)
            return JSONResponse(content=result)
            
        except Exception as e:
            logger.error(f"Chat input processing failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/input/file")
    async def handle_file_upload(
        file: UploadFile = File(...),
        source: str = Form("file_upload"),
        priority: str = Form("medium"),
        metadata: str = Form("{}")
    ):
        """Handle file upload (PDF, CSV, Excel, etc.)"""
        try:
            # Determine format from file extension
            file_format = InputFormat.UNKNOWN
            if file.filename:
                if file.filename.endswith('.pdf'):
                    file_format = InputFormat.PDF
                elif file.filename.endswith('.csv'):
                    file_format = InputFormat.CSV
                elif file.filename.endswith(('.xls', '.xlsx')):
                    file_format = InputFormat.EXCEL
                elif file.filename.endswith('.xml'):
                    file_format = InputFormat.XML
                elif file.filename.endswith('.json'):
                    file_format = InputFormat.JSON
                else:
                    file_format = InputFormat.TEXT
            
            # Read file content
            file_content = await file.read()
            
            context = InputContext(
                source=InputSource.FILE_UPLOAD,
                format=file_format,
                timestamp=datetime.utcnow(),
                metadata={
                    "filename": file.filename,
                    "content_type": file.content_type,
                    "file_size": len(file_content),
                    **json.loads(metadata)
                },
                priority=priority
            )
            
            result = await input_handler.process_input(file_content, context)
            return JSONResponse(content=result)
            
        except Exception as e:
            logger.error(f"File upload processing failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/input/webhook")
    async def handle_webhook_input(request: Request):
        """Handle webhook input from external systems"""
        try:
            # Get raw body and headers
            body = await request.body()
            headers = dict(request.headers)
            
            # Determine format from content-type
            content_type = headers.get("content-type", "").lower()
            if "json" in content_type:
                file_format = InputFormat.JSON
                input_data = json.loads(body.decode())
            elif "xml" in content_type:
                file_format = InputFormat.XML
                input_data = body.decode()
            else:
                file_format = InputFormat.TEXT
                input_data = body.decode()
            
            context = InputContext(
                source=InputSource.WEBHOOK,
                format=file_format,
                timestamp=datetime.utcnow(),
                metadata={
                    "headers": headers,
                    "content_type": content_type,
                    "user_agent": headers.get("user-agent"),
                    "source_ip": request.client.host if request.client else None
                },
                priority="medium"
            )
            
            result = await input_handler.process_input(input_data, context)
            return JSONResponse(content=result)
            
        except Exception as e:
            logger.error(f"Webhook input processing failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/input/slack")
    async def handle_slack_input(request: Request):
        """Handle Slack slash command or event"""
        try:
            form_data = await request.form()
            
            # Slack sends form-encoded data
            slack_data = {
                "text": form_data.get("text", ""),
                "user_id": form_data.get("user_id"),
                "channel_id": form_data.get("channel_id"),
                "team_id": form_data.get("team_id"),
                "command": form_data.get("command")
            }
            
            context = InputContext(
                source=InputSource.SLACK,
                format=InputFormat.JSON,
                timestamp=datetime.utcnow(),
                sender_info={
                    "user_id": slack_data["user_id"],
                    "channel_id": slack_data["channel_id"],
                    "team_id": slack_data["team_id"]
                },
                priority="medium"
            )
            
            result = await input_handler.process_input(slack_data, context)
            
            # Return Slack-formatted response
            if result.get("success") and result.get("formatted_response"):
                slack_format = result["formatted_response"].get("slack_format", {})
                return JSONResponse(content=slack_format)
            else:
                return JSONResponse(content={
                    "text": f"Processing failed: {result.get('error', 'Unknown error')}"
                })
            
        except Exception as e:
            logger.error(f"Slack input processing failed: {e}")
            return JSONResponse(content={
                "text": f"Error processing request: {str(e)}"
            })
    
    @app.post("/api/input/teams")
    async def handle_teams_input(request: Request):
        """Handle Microsoft Teams input"""
        try:
            teams_data = await request.json()
            
            # Extract message from Teams webhook
            message_text = ""
            if "text" in teams_data:
                message_text = teams_data["text"]
            elif "value" in teams_data and "text" in teams_data["value"]:
                message_text = teams_data["value"]["text"]
            
            context = InputContext(
                source=InputSource.TEAMS,
                format=InputFormat.JSON,
                timestamp=datetime.utcnow(),
                sender_info=teams_data.get("from", {}),
                metadata=teams_data,
                priority="medium"
            )
            
            result = await input_handler.process_input(message_text, context)
            return JSONResponse(content=result)
            
        except Exception as e:
            logger.error(f"Teams input processing failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/input/sms")
    async def handle_sms_input(
        message: str = Form(...),
        from_number: str = Form(...),
        to_number: str = Form(...),
        message_sid: str = Form(None)
    ):
        """Handle SMS input (Twilio format)"""
        try:
            context = InputContext(
                source=InputSource.SMS,
                format=InputFormat.TEXT,
                timestamp=datetime.utcnow(),
                sender_info={
                    "from_number": from_number,
                    "to_number": to_number,
                    "message_sid": message_sid
                },
                priority="high"  # SMS typically urgent
            )
            
            result = await input_handler.process_input(message, context)
            
            # Return SMS-friendly response
            if result.get("success"):
                return JSONResponse(content={
                    "message": "Request processed successfully. You will receive updates via email."
                })
            else:
                return JSONResponse(content={
                    "message": f"Processing failed: {result.get('error', 'Unknown error')}"
                })
            
        except Exception as e:
            logger.error(f"SMS input processing failed: {e}")
            return JSONResponse(content={
                "message": f"Error: {str(e)}"
            })
    
    @app.get("/api/input/sources")
    async def get_supported_sources():
        """Get list of supported input sources and formats"""
        return JSONResponse(content={
            "supported_sources": [source.value for source in InputSource],
            "supported_formats": [format.value for format in InputFormat],
            "endpoints": [
                "/api/input/text",
                "/api/input/json", 
                "/api/input/email",
                "/api/input/chat",
                "/api/input/file",
                "/api/input/webhook",
                "/api/input/slack",
                "/api/input/teams",
                "/api/input/sms"
            ],
            "capabilities": input_handler.get_handler_capabilities()
        })
    
    return app