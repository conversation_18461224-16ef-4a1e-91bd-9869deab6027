# docker-compose-simple.yml - Simplified AI Agent Stack for Development

services:
  # ========== DATABASE SERVICE ==========
  postgres:
    image: pgvector/pgvector:pg16
    container_name: ai-agent-postgres
    environment:
      POSTGRES_DB: agent_db
      POSTGRES_USER: agent_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent_user -d agent_db"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - agent-network

  # ========== REDIS CACHE SERVICE ==========
  redis:
    image: redis:7.2-alpine
    container_name: ai-agent-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - agent-network

  # ========== MAIN AGENT SERVICE ==========
  agent-service:
    build:
      context: .
      dockerfile: services/agent/Dockerfile
      target: production
      args:
        - PYTHON_VERSION=3.11
        - BUILD_DATE=${BUILD_DATE}
        - GIT_COMMIT=${GIT_COMMIT}
    container_name: ai-agent-service
    environment:
      - DATABASE_URL=postgresql://agent_user:${POSTGRES_PASSWORD}@postgres:5432/agent_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
    ports:
      - "8000:8000"
      - "8001:8001"  # Health check port
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - agent-network

# ========== NETWORKS ==========
networks:
  agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ========== VOLUMES ==========
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local