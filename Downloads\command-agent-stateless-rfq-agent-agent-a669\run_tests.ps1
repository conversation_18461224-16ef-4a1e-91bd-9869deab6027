#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Comprehensive Test Runner for LangGraph AI Agent System (PowerShell)

.DESCRIPTION
    Runs different categories of tests for the AI Agent system.
    Supports unit, integration, factor-specific, and comprehensive testing.

.PARAMETER Command
    The test command to run: unit, integration, e2e, factor, factors, performance, coverage, quick, comprehensive, api, setup, report

.PARAMETER Factor
    Specific factor to test (1-12) when using 'factor' command

.PARAMETER File
    Specific test file to run

.EXAMPLE
    .\run_tests.ps1 quick
    Runs quick test suite (unit tests only)

.EXAMPLE
    .\run_tests.ps1 factor -Factor 10
    Runs tests for Factor 10 only

.EXAMPLE
    .\run_tests.ps1 comprehensive
    Runs complete test suite with coverage
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("unit", "integration", "e2e", "factor", "factors", "performance", "coverage", "quick", "comprehensive", "api", "setup", "report")]
    [string]$Command,
    
    [Parameter(Mandatory=$false)]
    [ValidateRange(1, 12)]
    [int]$Factor,
    
    [Parameter(Mandatory=$false)]
    [string]$File
)

# Script variables
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = $ScriptDir
$TestDir = Join-Path $ProjectRoot "tests"

# Color functions for output
function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Cyan
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Header {
    param([string]$Message)
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor Blue
    Write-Host $Message -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Blue
}

# Function to run pytest commands
function Invoke-PytestCommand {
    param(
        [string[]]$Arguments,
        [string]$Description
    )
    
    Write-Header "Running: $Description"
    Write-Info "Command: python -m pytest $($Arguments -join ' ')"
    
    $StartTime = Get-Date
    
    try {
        $Process = Start-Process -FilePath "python" -ArgumentList @("-m", "pytest") + $Arguments -WorkingDirectory $ProjectRoot -Wait -PassThru -NoNewWindow
        $EndTime = Get-Date
        $Duration = ($EndTime - $StartTime).TotalSeconds
        
        if ($Process.ExitCode -eq 0) {
            Write-Success "$Description completed successfully in $("{0:F2}" -f $Duration)s"
            return $true
        } else {
            Write-Error "$Description failed after $("{0:F2}" -f $Duration)s"
            return $false
        }
    }
    catch {
        Write-Error "Failed to execute pytest: $($_.Exception.Message)"
        return $false
    }
}

# Test environment setup
function Initialize-TestEnvironment {
    Write-Header "Setting up test environment"
    
    # Set environment variables
    $env:ENVIRONMENT = "testing"
    $env:LOG_LEVEL = "DEBUG" 
    $env:DATABASE_URL = "sqlite:///test.db"
    $env:REDIS_URL = "redis://localhost:6379/15"
    $env:PERPLEXITY_API_KEY = "test-key-12345"
    
    # Check Python installation
    try {
        $PythonVersion = python --version 2>&1
        Write-Success "Python: $PythonVersion"
    }
    catch {
        Write-Error "Python not found. Please install Python 3.8+ and add to PATH"
        return $false
    }
    
    # Check pytest installation
    try {
        $PytestOutput = python -m pytest --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Pytest: $PytestOutput"
        } else {
            Write-Error "Pytest not installed. Run: pip install pytest pytest-asyncio pytest-cov"
            return $false
        }
    }
    catch {
        Write-Error "Failed to check pytest installation"
        return $false
    }
    
    # Check test directory
    if (-not (Test-Path $TestDir)) {
        Write-Error "Test directory not found: $TestDir"
        return $false
    }
    
    Write-Success "Test environment setup complete"
    return $true
}

# Test runner functions
function Invoke-UnitTests {
    return Invoke-PytestCommand @("-m", "unit", "--verbose", "--tb=short") "Unit Tests"
}

function Invoke-IntegrationTests {
    return Invoke-PytestCommand @("-m", "integration", "--verbose", "--tb=short") "Integration Tests"
}

function Invoke-E2ETests {
    return Invoke-PytestCommand @("-m", "e2e", "--verbose", "--tb=long") "End-to-End Tests"
}

function Invoke-FactorTests {
    param([int]$FactorNumber)
    return Invoke-PytestCommand @("-m", "factor$FactorNumber", "--verbose") "Factor $FactorNumber Tests"
}

function Invoke-AllFactorTests {
    Write-Header "Testing All 12 Factors"
    $SuccessCount = 0
    
    for ($i = 1; $i -le 12; $i++) {
        Write-Info "Testing Factor $i"
        if (Invoke-FactorTests -FactorNumber $i) {
            $SuccessCount++
        } else {
            Write-Error "Factor $i tests failed"
        }
    }
    
    Write-Header "Factor Tests Summary: $SuccessCount/12 factors passed"
    return ($SuccessCount -eq 12)
}

function Invoke-PerformanceTests {
    return Invoke-PytestCommand @("-m", "slow", "--verbose", "--durations=10") "Performance Tests"
}

function Invoke-CoverageTests {
    return Invoke-PytestCommand @("--cov=services/agent", "--cov-report=html", "--cov-report=term-missing", "--cov-report=xml", "--verbose") "All Tests with Coverage"
}

function Invoke-QuickTests {
    return Invoke-PytestCommand @("-m", "unit and not slow", "--verbose", "--tb=line", "--maxfail=5") "Quick Test Suite"
}

function Invoke-APITests {
    return Invoke-PytestCommand @("tests/test_api_integration.py", "--verbose") "API Integration Tests"
}

function Invoke-ComprehensiveTests {
    Write-Header "Starting Comprehensive Test Suite"
    Write-Info "This includes all test types with detailed reporting"
    
    $SuccessCount = 0
    $TotalTestTypes = 4
    
    # 1. Unit Tests
    if (Invoke-UnitTests) { $SuccessCount++ }
    
    # 2. Integration Tests
    if (Invoke-IntegrationTests) { $SuccessCount++ }
    
    # 3. All Factor Tests
    if (Invoke-AllFactorTests) { $SuccessCount++ }
    
    # 4. Coverage Report
    if (Invoke-CoverageTests) { $SuccessCount++ }
    
    Write-Header "Comprehensive Test Summary"
    Write-Info "Successful test categories: $SuccessCount/$TotalTestTypes"
    
    if ($SuccessCount -eq $TotalTestTypes) {
        Write-Success "All tests passed! System is ready for deployment."
        return $true
    } else {
        Write-Warning "Some test categories failed. Review results above."
        return $false
    }
}

function Invoke-TestReport {
    return Invoke-PytestCommand @("--html=test_report.html", "--self-contained-html", "--verbose") "Test Report Generation"
}

function Invoke-SpecificTestFile {
    param([string]$TestFile)
    return Invoke-PytestCommand @($TestFile, "--verbose") "Test File: $TestFile"
}

# Main execution
function Main {
    Write-Header "LangGraph AI Agent Test Runner (PowerShell)"
    
    # Setup test environment
    if (-not (Initialize-TestEnvironment)) {
        Write-Error "Failed to setup test environment"
        exit 1
    }
    
    $Success = $false
    
    # Execute based on command
    switch ($Command) {
        "setup" {
            $Success = $true  # Already done above
        }
        "unit" {
            $Success = Invoke-UnitTests
        }
        "integration" {
            $Success = Invoke-IntegrationTests
        }
        "e2e" {
            $Success = Invoke-E2ETests
        }
        "factor" {
            if ($Factor) {
                $Success = Invoke-FactorTests -FactorNumber $Factor
            } else {
                Write-Error "--Factor parameter required for factor command"
                exit 1
            }
        }
        "factors" {
            $Success = Invoke-AllFactorTests
        }
        "performance" {
            $Success = Invoke-PerformanceTests
        }
        "coverage" {
            $Success = Invoke-CoverageTests
        }
        "quick" {
            $Success = Invoke-QuickTests
        }
        "comprehensive" {
            $Success = Invoke-ComprehensiveTests
        }
        "api" {
            $Success = Invoke-APITests
        }
        "report" {
            $Success = Invoke-TestReport
        }
    }
    
    # Handle specific file if provided
    if ($File) {
        $Success = Invoke-SpecificTestFile -TestFile $File
    }
    
    # Final result
    if ($Success) {
        Write-Success "Test command '$Command' completed successfully!"
        exit 0
    } else {
        Write-Error "Test command '$Command' failed!"
        exit 1
    }
}

# Execute main function
Main