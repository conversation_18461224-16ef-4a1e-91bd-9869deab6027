#!/bin/bash
# Startup script for AI Agent service

set -e

echo "Starting AI Agent service..."

# Wait for database to be ready
echo "Waiting for database connection..."
python -c "
import time
import psycopg2
import os
from urllib.parse import urlparse

db_url = os.getenv('DATABASE_URL', '**********************************************/agent_db')
parsed = urlparse(db_url)

max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port,
            user=parsed.username,
            password=parsed.password,
            database=parsed.path[1:]
        )
        conn.close()
        print('Database is ready!')
        break
    except psycopg2.OperationalError:
        retry_count += 1
        print(f'Database not ready, retrying... ({retry_count}/{max_retries})')
        time.sleep(2)
else:
    print('Failed to connect to database after maximum retries')
    exit(1)
"

# Start the application
echo "Starting application..."
exec "$@"
