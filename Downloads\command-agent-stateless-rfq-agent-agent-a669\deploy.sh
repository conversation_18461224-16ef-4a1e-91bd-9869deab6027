#!/bin/bash
# deploy.sh - Production deployment script for AI Agent System
# Implements comprehensive deployment with monitoring and validation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_PROJECT_NAME="ai-agents"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="./logs/deployment_$(date +%Y%m%d_%H%M%S).log"

# Create log directory
mkdir -p logs

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check available disk space (minimum 10GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 10485760 ]; then  # 10GB in KB
        warning "Less than 10GB disk space available"
    fi
    
    # Check environment file
    if [ ! -f .env.production ]; then
        error ".env.production file not found"
        exit 1
    fi
    
    log "Prerequisites check completed"
}

# Create required directories
create_directories() {
    log "Creating required directories..."
    
    directories=(
        "logs/agent"
        "logs/orchestrator" 
        "logs/memory"
        "logs/nginx"
        "logs/postgres"
        "logs/redis"
        "secrets"
        "monitoring/prometheus"
        "monitoring/grafana/dashboards"
        "monitoring/grafana/datasources"
        "monitoring/fluentd/conf"
        "database/backups"
        "models"
        "ssl"
        "scripts"
        "config"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        info "Created directory: $dir"
    done
    
    log "Directory creation completed"
}

# Generate secrets
generate_secrets() {
    log "Generating secrets..."
    
    # Generate passwords if they don't exist
    if [ ! -f secrets/postgres_password.txt ]; then
        openssl rand -base64 32 > secrets/postgres_password.txt
        info "Generated PostgreSQL password"
    fi
    
    if [ ! -f secrets/redis_password.txt ]; then
        openssl rand -base64 32 > secrets/redis_password.txt
        info "Generated Redis password"
    fi
    
    if [ ! -f secrets/rabbitmq_password.txt ]; then
        openssl rand -base64 32 > secrets/rabbitmq_password.txt
        info "Generated RabbitMQ password"
    fi
    
    if [ ! -f secrets/grafana_password.txt ]; then
        openssl rand -base64 32 > secrets/grafana_password.txt
        info "Generated Grafana password"
    fi
    
    if [ ! -f secrets/jwt_secret.txt ]; then
        openssl rand -base64 64 > secrets/jwt_secret.txt
        info "Generated JWT secret"
    fi
    
    # Set proper permissions
    chmod 600 secrets/*
    
    log "Secret generation completed"
}

# Setup environment variables
setup_environment() {
    log "Setting up environment variables..."
    
    # Copy production environment template
    if [ ! -f .env ]; then
        cp .env.production .env
        info "Created .env from production template"
    fi
    
    # Set build-time variables
    export BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
    export GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    export COMPOSE_PROJECT_NAME="$COMPOSE_PROJECT_NAME"
    
    # Load secrets into environment
    export POSTGRES_PASSWORD=$(cat secrets/postgres_password.txt)
    export REDIS_PASSWORD=$(cat secrets/redis_password.txt)
    export RABBITMQ_PASSWORD=$(cat secrets/rabbitmq_password.txt)
    export GRAFANA_PASSWORD=$(cat secrets/grafana_password.txt)
    export JWT_SECRET=$(cat secrets/jwt_secret.txt)
    
    info "BUILD_DATE: $BUILD_DATE"
    info "GIT_COMMIT: $GIT_COMMIT"
    
    log "Environment setup completed"
}

# Create configuration files
create_configs() {
    log "Creating configuration files..."
    
    # Nginx configuration
    cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream agent_backend {
        least_conn;
        server agent-service:8000 max_fails=3 fail_timeout=30s;
        server langgraph-orchestrator:8002 max_fails=3 fail_timeout=30s backup;
    }

    upstream memory_backend {
        server memory-service:8003 max_fails=3 fail_timeout=30s;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=memory:10m rate=5r/s;

    # Logging
    log_format detailed '$remote_addr - $remote_user [$time_local] '
                       '"$request" $status $body_bytes_sent '
                       '"$http_referer" "$http_user_agent" '
                       '$request_time $upstream_response_time';

    server {
        listen 80;
        server_name _;
        
        access_log /var/log/nginx/access.log detailed;
        error_log /var/log/nginx/error.log warn;

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://agent_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Memory service routes
        location /memory/ {
            limit_req zone=memory burst=10 nodelay;
            proxy_pass http://memory_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # Frontend static files
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }
}
EOF

    # Prometheus configuration
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'agent-service'
    static_configs:
      - targets: ['agent-service:8001']
    scrape_interval: 10s
    metrics_path: /metrics
    
  - job_name: 'memory-service'
    static_configs:
      - targets: ['memory-service:8003']
    scrape_interval: 10s
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
EOF

    # Redis configuration
    cat > redis/redis.conf << 'EOF'
# Redis configuration for AI Agent system
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
dbfilename dump.rdb
dir /data
EOF

    # RabbitMQ configuration
    cat > rabbitmq/rabbitmq.conf << 'EOF'
# RabbitMQ configuration
management.listener.port = 15672
management.listener.ssl = false
default_user = agent_user
default_pass = CHANGE_PASSWORD
default_vhost = agent_vhost
disk_free_limit.absolute = 2GB
vm_memory_high_watermark.absolute = 1GB
EOF

    log "Configuration files created"
}

# Backup existing data
backup_existing_data() {
    log "Creating backup of existing data..."
    
    if [ -d "postgres_data" ] || [ -d "redis_data" ] || [ -d "grafana_data" ]; then
        mkdir -p "$BACKUP_DIR"
        
        if [ -d "postgres_data" ]; then
            cp -r postgres_data "$BACKUP_DIR/"
            info "Backed up PostgreSQL data"
        fi
        
        if [ -d "redis_data" ]; then
            cp -r redis_data "$BACKUP_DIR/"
            info "Backed up Redis data"
        fi
        
        if [ -d "grafana_data" ]; then
            cp -r grafana_data "$BACKUP_DIR/"
            info "Backed up Grafana data"
        fi
        
        log "Backup completed: $BACKUP_DIR"
    else
        info "No existing data to backup"
    fi
}

# Build Docker images
build_images() {
    log "Building Docker images..."
    
    # Build with build args
    docker-compose build \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --parallel
    
    log "Docker image build completed"
}

# Deploy services
deploy_services() {
    log "Deploying services..."
    
    # Start core infrastructure first
    info "Starting database and cache services..."
    docker-compose up -d postgres redis rabbitmq
    
    # Wait for services to be healthy
    info "Waiting for database to be ready..."
    timeout 60 bash -c 'until docker-compose exec postgres pg_isready -U agent_user -d agent_db; do sleep 2; done'
    
    info "Waiting for Redis to be ready..."
    timeout 30 bash -c 'until docker-compose exec redis redis-cli ping; do sleep 2; done'
    
    # Start application services
    info "Starting application services..."
    docker-compose up -d agent-service langgraph-orchestrator memory-service
    
    # Wait for application services
    info "Waiting for application services to be ready..."
    timeout 120 bash -c 'until curl -f http://localhost:8001/health; do sleep 5; done'
    
    # Start supporting services
    info "Starting supporting services..."
    docker-compose up -d celery-worker nginx
    
    # Start monitoring stack
    info "Starting monitoring services..."
    docker-compose up -d prometheus grafana fluentd
    
    log "Service deployment completed"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Wait a bit more for the database to be fully ready
    sleep 10
    
    # Run database initialization
    docker-compose exec postgres psql -U agent_user -d agent_db -f /docker-entrypoint-initdb.d/01_init_schema.sql || true
    
    info "Database migrations completed"
}

# Validate deployment
validate_deployment() {
    log "Validating deployment..."
    
    # Check service health
    services=("postgres" "redis" "agent-service" "langgraph-orchestrator" "memory-service" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            info "✓ $service is running"
        else
            error "✗ $service is not running"
            return 1
        fi
    done
    
    # Test API endpoints
    info "Testing API endpoints..."
    
    if curl -f http://localhost/health > /dev/null 2>&1; then
        info "✓ Main API health check passed"
    else
        error "✗ Main API health check failed"
        return 1
    fi
    
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        info "✓ Agent service health check passed"
    else
        error "✗ Agent service health check failed"
        return 1
    fi
    
    # Check monitoring
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        info "✓ Grafana is accessible"
    else
        warning "Grafana may not be ready yet"
    fi
    
    if curl -f http://localhost:9090 > /dev/null 2>&1; then
        info "✓ Prometheus is accessible"
    else
        warning "Prometheus may not be ready yet"
    fi
    
    log "Deployment validation completed"
}

# Display deployment information
show_deployment_info() {
    log "Deployment completed successfully!"
    
    echo ""
    echo "=========================================="
    echo "         AI AGENT SYSTEM DEPLOYED"
    echo "=========================================="
    echo ""
    echo "Services:"
    echo "  • Main API: http://localhost/api"
    echo "  • Agent Service: http://localhost:8000"
    echo "  • Memory Service: http://localhost:8003"
    echo "  • Grafana Dashboard: http://localhost:3000"
    echo "  • Prometheus Metrics: http://localhost:9090"
    echo "  • RabbitMQ Management: http://localhost:15672"
    echo ""
    echo "Credentials:"
    echo "  • Grafana: admin / $(cat secrets/grafana_password.txt)"
    echo "  • RabbitMQ: agent_user / $(cat secrets/rabbitmq_password.txt)"
    echo ""
    echo "Logs:"
    echo "  • Deployment log: $LOG_FILE"
    echo "  • Service logs: docker-compose logs -f [service_name]"
    echo ""
    echo "Management:"
    echo "  • View status: docker-compose ps"
    echo "  • Stop services: docker-compose down"
    echo "  • View logs: docker-compose logs -f"
    echo ""
    echo "Backup location: $BACKUP_DIR"
    echo "=========================================="
}

# Cleanup function
cleanup() {
    log "Cleaning up..."
    # Any cleanup tasks if needed
}

# Main deployment function
main() {
    log "Starting AI Agent System deployment..."
    
    # Set trap for cleanup on exit
    trap cleanup EXIT
    
    # Run deployment steps
    check_prerequisites
    create_directories
    generate_secrets
    setup_environment
    create_configs
    backup_existing_data
    build_images
    deploy_services
    run_migrations
    validate_deployment
    show_deployment_info
    
    log "Deployment completed successfully!"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log "Stopping AI Agent System..."
        docker-compose down
        log "System stopped"
        ;;
    "restart")
        log "Restarting AI Agent System..."
        docker-compose restart
        log "System restarted"
        ;;
    "logs")
        docker-compose logs -f "${2:-}"
        ;;
    "status")
        docker-compose ps
        ;;
    "backup")
        backup_existing_data
        ;;
    "help")
        echo "Usage: $0 {deploy|stop|restart|logs|status|backup|help}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Deploy the entire system (default)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services" 
        echo "  logs    - View logs (optional service name)"
        echo "  status  - Show service status"
        echo "  backup  - Create backup of data"
        echo "  help    - Show this help message"
        ;;
    *)
        error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac