"""
Pydantic schemas for RFQ workflow data validation and serialization
Implements structured data models for Factor 4: Tools Are Just Structured Outputs
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, Any, List, Optional, Literal
from datetime import datetime
from enum import Enum

class UrgencyLevel(str, Enum):
    """Enumeration for urgency levels"""
    LOW = "low"
    STANDARD = "standard"
    HIGH = "high"
    CRITICAL = "critical"

class WorkflowStage(str, Enum):
    """Enumeration for workflow stages"""
    DISCOVERY = "discovery"
    GENERATION = "generation"
    DISTRIBUTION = "distribution"
    EVALUATION = "evaluation"
    AWARD = "award"

class ExecutionState(str, Enum):
    """Enumeration for execution states"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# ========== INPUT SCHEMAS ==========

class RFQRequest(BaseModel):
    """Input schema for RFQ processing requests"""
    natural_language_request: str = Field(
        ..., 
        min_length=10,
        max_length=5000,
        description="Natural language description of procurement needs"
    )
    user_id: str = Field(..., description="ID of the requesting user")
    request_id: Optional[str] = Field(None, description="Optional request tracking ID")
    department: Optional[str] = Field(None, description="Requesting department")
    urgency: Optional[UrgencyLevel] = Field(UrgencyLevel.STANDARD, description="Request urgency level")
    budget_range: Optional[str] = Field(None, description="Budget range or constraints")
    preferred_region: Optional[str] = Field("India", description="Preferred supplier region")
    additional_context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    @validator('natural_language_request')
    def validate_request(cls, v):
        if not v.strip():
            raise ValueError('Request cannot be empty')
        return v.strip()
    
    class Config:
        json_schema_extra = {
            "example": {
                "natural_language_request": "I need 50 business laptops for our IT department. Budget around $50,000. Need them urgently for new employee onboarding in Bangalore.",
                "user_id": "user_123",
                "department": "IT",
                "urgency": "high",
                "budget_range": "$50,000",
                "preferred_region": "Bangalore"
            }
        }

# ========== CORE DATA MODELS ==========

class VendorInfo(BaseModel):
    """Vendor information model"""
    id: str = Field(..., description="Unique vendor identifier")
    name: str = Field(..., description="Vendor name")
    email: str = Field(..., description="Vendor contact email")
    rating: float = Field(..., ge=0, le=5, description="Vendor rating (0-5)")
    specialties: List[str] = Field(default_factory=list, description="Vendor specialties")
    location: str = Field(..., description="Vendor location")
    contact_info: Optional[Dict[str, Any]] = Field(default_factory=dict)
    performance_history: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "vendor_12345",
                "name": "Premium IT Solutions Pvt Ltd",
                "email": "<EMAIL>",
                "rating": 4.8,
                "specialties": ["Business Laptops", "IT Equipment"],
                "location": "Bangalore"
            }
        }

class Quote(BaseModel):
    """Quote information model"""
    vendor_id: str = Field(..., description="Vendor identifier")
    price: float = Field(..., ge=0, description="Quoted price")
    delivery_time: str = Field(..., description="Delivery timeframe")
    terms: str = Field(..., description="Terms and conditions")
    specifications_met: Optional[bool] = Field(None)
    additional_services: Optional[List[str]] = Field(default_factory=list)
    validity_period: Optional[str] = Field(None)

class MarketIntelligence(BaseModel):
    """Market intelligence data model"""
    price_range: str = Field(..., description="Market price range analysis")
    market_trends: str = Field(..., description="Current market trends")
    supplier_recommendations: List[str] = Field(default_factory=list)
    risk_factors: List[str] = Field(default_factory=list)
    compliance_requirements: List[str] = Field(default_factory=list)
    research_timestamp: Optional[str] = Field(None)
    perplexity_insights: Optional[str] = Field(None)
    
    class Config:
        json_schema_extra = {
            "example": {
                "price_range": "$800-$1200 per business laptop",
                "market_trends": "Stable pricing with 8-12% annual growth",
                "supplier_recommendations": ["Dell Technologies", "HP Enterprise"],
                "risk_factors": ["Supply chain delays", "Price volatility"],
                "compliance_requirements": ["ISO 9001:2015", "Regional ESG standards"]
            }
        }

class RFQDocument(BaseModel):
    """RFQ document model"""
    title: str = Field(..., description="RFQ document title")
    description: str = Field(..., description="Detailed description")
    specifications: List[str] = Field(..., description="Technical specifications")
    quantity: int = Field(..., ge=1, description="Required quantity")
    delivery_requirements: str = Field(..., description="Delivery requirements")
    evaluation_criteria: List[str] = Field(..., description="Evaluation criteria")
    submission_deadline: str = Field(..., description="Submission deadline")
    terms_and_conditions: Optional[str] = Field(None)
    
    @validator('submission_deadline')
    def validate_deadline(cls, v):
        try:
            datetime.fromisoformat(v.replace('Z', '+00:00'))
        except:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except:
                raise ValueError('Invalid deadline format')
        return v

class CommunicationResults(BaseModel):
    """Communication results model"""
    total_sent: int = Field(..., ge=0, description="Total RFQs sent")
    successful_deliveries: int = Field(..., ge=0, description="Successful deliveries")
    failed_deliveries: int = Field(..., ge=0, description="Failed deliveries")
    delivery_results: List[Dict[str, Any]] = Field(default_factory=list)
    rfq_title: str = Field(..., description="RFQ title")
    submission_deadline: str = Field(..., description="Submission deadline")

# ========== STATE MODELS ==========

class RFQState(BaseModel):
    """RFQ workflow state model"""
    rfq_id: str = Field(..., description="Unique RFQ identifier")
    current_stage: WorkflowStage = Field(..., description="Current workflow stage")
    workflow_step: str = Field(..., description="Current workflow step")
    progress_percentage: int = Field(..., ge=0, le=100, description="Progress percentage")
    vendors: List[VendorInfo] = Field(default_factory=list)
    quotes_received: List[Quote] = Field(default_factory=list)
    market_intelligence: Optional[MarketIntelligence] = Field(None)
    human_approval_pending: bool = Field(False, description="Human approval required")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    error_info: Optional[Dict[str, Any]] = Field(None)

class WorkflowExecution(BaseModel):
    """Workflow execution model"""
    id: str = Field(..., description="Execution identifier")
    workflow_id: str = Field(..., description="Workflow identifier")
    user_id: str = Field(..., description="User identifier")
    input_data: Dict[str, Any] = Field(..., description="Input data")
    current_state: Dict[str, Any] = Field(..., description="Current state")
    execution_state: ExecutionState = Field(..., description="Execution state")
    progress_percentage: int = Field(0, ge=0, le=100)
    error_info: Optional[Dict[str, Any]] = Field(None)
    started_at: str = Field(..., description="Start timestamp")
    completed_at: Optional[str] = Field(None, description="Completion timestamp")

class HumanTask(BaseModel):
    """Human task model for approval workflows"""
    id: str = Field(..., description="Task identifier")
    execution_id: str = Field(..., description="Related execution ID")
    task_type: str = Field(..., description="Type of human task")
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Task description")
    context_data: Dict[str, Any] = Field(..., description="Context data for task")
    assigned_to: Optional[str] = Field(None, description="Assigned user")
    priority: int = Field(3, ge=1, le=5, description="Task priority (1-5)")
    status: str = Field("pending", description="Task status")
    response_data: Optional[Dict[str, Any]] = Field(None)
    deadline: Optional[str] = Field(None, description="Task deadline")
    created_at: str = Field(..., description="Creation timestamp")
    completed_at: Optional[str] = Field(None, description="Completion timestamp")

# ========== RESPONSE SCHEMAS ==========

class SystemInfo(BaseModel):
    """System information model"""
    workflow_id: str = Field(..., description="Workflow identifier")
    processing_time: str = Field(..., description="Processing timestamp")
    api_used: str = Field(..., description="API used for processing")
    fallback_used: bool = Field(False, description="Whether fallback was used")
    tool_calls_count: Optional[int] = Field(None, description="Number of tool calls made")

class VendorsContacted(BaseModel):
    """Vendors contacted information"""
    vendors: List[VendorInfo] = Field(..., description="List of contacted vendors")
    total_vendors_found: int = Field(..., description="Total vendors found")
    perplexity_vendor_insights: Optional[str] = Field(None, description="AI insights about vendors")

class RFQResponse(BaseModel):
    """Complete RFQ workflow response"""
    success: bool = Field(..., description="Whether workflow succeeded")
    execution_id: str = Field(..., description="Execution identifier")
    rfq_state: RFQState = Field(..., description="Current RFQ state")
    workflow_summary: str = Field(..., description="Executive summary")
    market_intelligence: MarketIntelligence = Field(..., description="Market intelligence")
    vendors_contacted: VendorsContacted = Field(..., description="Vendor information")
    rfq_document: RFQDocument = Field(..., description="Generated RFQ document")
    communication_results: CommunicationResults = Field(..., description="Communication results")
    next_actions: List[str] = Field(..., description="Recommended next actions")
    system_info: SystemInfo = Field(..., description="System processing information")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "execution_id": "exec_12345",
                "rfq_state": {
                    "rfq_id": "rfq_20240101_12345",
                    "current_stage": "distribution",
                    "workflow_step": "rfq_sent_to_vendors",
                    "progress_percentage": 60,
                    "human_approval_pending": False
                },
                "workflow_summary": "RFQ processed successfully for IT department...",
                "next_actions": [
                    "Monitor vendor responses",
                    "Follow up after 3 days"
                ]
            }
        }

class ErrorResponse(BaseModel):
    """Error response model"""
    success: bool = Field(False, description="Always false for errors")
    execution_id: str = Field(..., description="Execution identifier")
    error: str = Field(..., description="Error message")
    current_step: str = Field(..., description="Step where error occurred")
    progress_percentage: int = Field(..., description="Progress before error")
    error_info: Optional[Dict[str, Any]] = Field(None, description="Detailed error information")
    fallback_available: bool = Field(True, description="Whether fallback is available")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())

# ========== TOOL SCHEMAS ==========

class ToolCall(BaseModel):
    """Tool call record"""
    tool_name: str = Field(..., description="Name of the tool called")
    input_data: Dict[str, Any] = Field(..., description="Input data for tool")
    output_data: Dict[str, Any] = Field(..., description="Output from tool")
    execution_time: float = Field(..., description="Execution time in seconds")
    timestamp: str = Field(..., description="Call timestamp")
    success: bool = Field(..., description="Whether call succeeded")
    error_info: Optional[str] = Field(None, description="Error information if failed")

class ToolDefinition(BaseModel):
    """Tool definition model"""
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    schema: Dict[str, Any] = Field(..., description="Tool input schema")
    implementation: str = Field(..., description="Tool implementation reference")
    category: Optional[str] = Field(None, description="Tool category")
    version: str = Field("1.0.0", description="Tool version")
    is_active: bool = Field(True, description="Whether tool is active")

# ========== MEMORY SCHEMAS ==========

class MemoryItem(BaseModel):
    """Memory item model"""
    id: str = Field(..., description="Memory identifier")
    content: str = Field(..., description="Memory content")
    memory_type: str = Field(..., description="Type of memory")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Memory metadata")
    importance_score: float = Field(..., ge=0, le=1, description="Importance score")
    similarity: Optional[float] = Field(None, ge=0, le=1, description="Similarity score")
    access_count: int = Field(0, description="Access count")
    last_accessed: Optional[str] = Field(None, description="Last access timestamp")
    created_at: str = Field(..., description="Creation timestamp")

class MemorySearchRequest(BaseModel):
    """Memory search request"""
    query: str = Field(..., min_length=1, description="Search query")
    limit: int = Field(10, ge=1, le=50, description="Maximum results")
    memory_type: Optional[str] = Field(None, description="Filter by memory type")
    similarity_threshold: float = Field(0.7, ge=0, le=1, description="Similarity threshold")

class MemorySearchResponse(BaseModel):
    """Memory search response"""
    results: List[MemoryItem] = Field(..., description="Search results")
    total_found: int = Field(..., description="Total results found")
    query: str = Field(..., description="Original query")
    search_time: float = Field(..., description="Search time in seconds")

# ========== WORKFLOW CONTROL SCHEMAS ==========

class WorkflowControlRequest(BaseModel):
    """Workflow control request"""
    execution_id: str = Field(..., description="Execution to control")
    action: Literal["pause", "resume", "cancel"] = Field(..., description="Control action")
    reason: Optional[str] = Field(None, description="Reason for action")

class WorkflowStatusResponse(BaseModel):
    """Workflow status response"""
    execution_id: str = Field(..., description="Execution identifier")
    execution_state: ExecutionState = Field(..., description="Current execution state")
    current_step: str = Field(..., description="Current workflow step")
    progress_percentage: int = Field(..., description="Progress percentage")
    started_at: str = Field(..., description="Start timestamp")
    completed_at: Optional[str] = Field(None, description="Completion timestamp")
    error_info: Optional[Dict[str, Any]] = Field(None, description="Error information")

class HumanTaskResponse(BaseModel):
    """Human task response"""
    task_id: str = Field(..., description="Task identifier")
    approved: bool = Field(..., description="Whether request is approved")
    comments: Optional[str] = Field(None, description="Additional comments")
    modifications: Optional[Dict[str, Any]] = Field(None, description="Requested modifications")
    escalate: bool = Field(False, description="Whether to escalate to higher authority")