"""
Updated React Agent Component for LangGraph Backend Integration
Integrates with the new 12-Factor Agent system
"""

"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import ReactMarkdown from 'react-markdown';
import { 
  Send, 
  FileText, 
  Users, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Building,
  Play,
  Pause,
  RefreshCw,
  Eye,
  MessageSquare,
  BarChart3,
  Settings
} from "lucide-react";

// Enhanced interfaces for new backend
interface WorkflowStatus {
  execution_id: string;
  execution_state: "pending" | "running" | "paused" | "completed" | "failed" | "cancelled";
  current_step: string;
  progress_percentage: number;
  started_at: string;
  completed_at?: string;
  error_info?: any;
}

interface HumanTask {
  id: string;
  execution_id: string;
  task_type: string;
  title: string;
  description: string;
  context_data: any;
  priority: number;
  status: string;
  deadline?: string;
  created_at: string;
}

interface MemorySearchResult {
  results: Array<{
    id: string;
    content: string;
    memory_type: string;
    similarity: number;
    created_at: string;
  }>;
  total_found: number;
  search_time: number;
}

// Keep existing interfaces for compatibility
interface RFQState {
  rfq_id: string;
  current_stage: string;
  workflow_step: string;
  progress_percentage: number;
  vendors: VendorInfo[];
  quotes_received: any[];
  market_intelligence?: MarketIntelligence;
  human_approval_pending: boolean;
  created_at: string;
  updated_at: string;
}

interface VendorInfo {
  id: string;
  name: string;
  email: string;
  rating: number;
  specialties: string[];
  location: string;
}

interface MarketIntelligence {
  price_range: string;
  market_trends: string;
  supplier_recommendations: string[];
  risk_factors: string[];
  compliance_requirements: string[];
}

interface WorkflowResult {
  success: boolean;
  execution_id: string;
  rfq_state: RFQState;
  workflow_summary: string;
  market_intelligence: MarketIntelligence;
  vendors_contacted: {
    vendors: VendorInfo[];
    total_vendors_found: number;
    perplexity_vendor_insights?: string;
  };
  rfq_document: any;
  communication_results: any;
  next_actions: string[];
  system_info: {
    workflow_id: string;
    processing_time: string;
    api_used: string;
    fallback_used: boolean;
    tool_calls_count?: number;
  };
}

export function Agent() {
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<WorkflowResult | null>(null);
  const [activeTab, setActiveTab] = useState("submit");
  const [workflowStatus, setWorkflowStatus] = useState<WorkflowStatus | null>(null);
  const [humanTasks, setHumanTasks] = useState<HumanTask[]>([]);
  const [memoryResults, setMemoryResults] = useState<MemorySearchResult | null>(null);
  const [executionId, setExecutionId] = useState<string | null>(null);

  // API base URL - update for your deployment
  const API_BASE = process.env.NODE_ENV === 'development' 
    ? "http://localhost:8000" 
    : "http://localhost";

  // Polling for workflow status updates
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (executionId && workflowStatus?.execution_state === "running") {
      intervalId = setInterval(async () => {
        try {
          const response = await fetch(`${API_BASE}/api/workflows/${executionId}/status`);
          if (response.ok) {
            const status = await response.json();
            setWorkflowStatus(status);
            
            // Stop polling if workflow is complete
            if (["completed", "failed", "cancelled"].includes(status.execution_state)) {
              clearInterval(intervalId);
            }
          }
        } catch (error) {
          console.error("Failed to fetch workflow status:", error);
        }
      }, 5000); // Poll every 5 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [executionId, workflowStatus?.execution_state, API_BASE]);

  // Load human tasks on mount
  useEffect(() => {
    loadHumanTasks();
  }, []);

  const handleSubmit = async () => {
    if (!input.trim()) {
      toast.error("Please enter your RFQ request", {
        closeButton: true,
        duration: 3000
      });
      return;
    }

    setIsLoading(true);

    try {
      const requestPayload = {
        natural_language_request: input.trim(),
        user_id: "user_" + Date.now(), // In production, get from auth
        department: "General",
        urgency: "standard",
        preferred_region: "India"
      };

      const response = await fetch(`${API_BASE}/api/rfq/process`, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify(requestPayload)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setResult(data);
        setExecutionId(data.execution_id);
        setWorkflowStatus({
          execution_id: data.execution_id,
          execution_state: "completed",
          current_step: data.rfq_state.workflow_step,
          progress_percentage: data.rfq_state.progress_percentage,
          started_at: data.rfq_state.created_at,
          completed_at: data.rfq_state.updated_at
        });

        toast.success("RFQ workflow completed successfully!", {
          description: `RFQ ${data.rfq_state.rfq_id} processed and sent to ${data.communication_results.total_sent} vendors`,
          closeButton: true,
          duration: 5000
        });

        // Switch to results tab
        setActiveTab("results");
      } else {
        throw new Error(data.error || "Workflow execution failed");
      }

    } catch (error) {
      console.error("Error processing RFQ request:", error);
      
      toast.error(error.message || "An error occurred while processing your RFQ request", {
        closeButton: true,
        duration: 10000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleWorkflowControl = async (action: "pause" | "resume" | "cancel") => {
    if (!executionId) return;

    try {
      const response = await fetch(`${API_BASE}/api/workflows/${executionId}/${action}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" }
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Workflow ${action}d successfully`);
        
        // Update status
        if (workflowStatus) {
          setWorkflowStatus({
            ...workflowStatus,
            execution_state: action === "pause" ? "paused" : action === "resume" ? "running" : "cancelled"
          });
        }
      } else {
        throw new Error(`Failed to ${action} workflow`);
      }
    } catch (error) {
      toast.error(`Failed to ${action} workflow: ${error.message}`);
    }
  };

  const loadHumanTasks = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/human-tasks`);
      if (response.ok) {
        const data = await response.json();
        setHumanTasks(data.tasks || []);
      }
    } catch (error) {
      console.error("Failed to load human tasks:", error);
    }
  };

  const handleTaskResponse = async (taskId: string, approved: boolean, comments?: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/human-tasks/${taskId}/respond`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          task_id: taskId,
          approved,
          comments: comments || "",
          modifications: {}
        })
      });

      if (response.ok) {
        toast.success(`Task ${approved ? "approved" : "rejected"} successfully`);
        loadHumanTasks(); // Reload tasks
      } else {
        throw new Error("Failed to respond to task");
      }
    } catch (error) {
      toast.error(`Failed to respond to task: ${error.message}`);
    }
  };

  const searchMemory = async (query: string) => {
    if (!query.trim()) return;

    try {
      const response = await fetch(`${API_BASE}/api/memory/search`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          query: query.trim(),
          limit: 10,
          similarity_threshold: 0.7
        })
      });

      if (response.ok) {
        const data = await response.json();
        setMemoryResults(data);
        setActiveTab("memory");
      } else {
        throw new Error("Memory search failed");
      }
    } catch (error) {
      toast.error(`Memory search failed: ${error.message}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit();
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl md:text-4xl font-bold">LangGraph AI Agent System</h1>
          <p className="text-muted-foreground text-lg">
            Enterprise AI Agent with 12-Factor principles, semantic memory, and workflow orchestration
          </p>
        </div>

        {/* Main Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="submit" className="flex items-center gap-2">
              <Send className="h-4 w-4" />
              Submit RFQ
            </TabsTrigger>
            <TabsTrigger value="results" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Results
            </TabsTrigger>
            <TabsTrigger value="workflow" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Workflow
            </TabsTrigger>
            <TabsTrigger value="tasks" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Human Tasks
            </TabsTrigger>
            <TabsTrigger value="memory" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Memory
            </TabsTrigger>
          </TabsList>

          {/* Submit RFQ Tab */}
          <TabsContent value="submit" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Submit RFQ Request
                </CardTitle>
                <CardDescription>
                  Describe your procurement needs. The AI agent will process your request using LangGraph workflows with semantic memory.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="Example: I need 50 business laptops for our IT department. Budget around $50,000. Need them urgently for new employee onboarding in Bangalore."
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyPress}
                  className="min-h-[120px] resize-none"
                  disabled={isLoading}
                />
                <div className="flex justify-between items-center">
                  <div className="space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => searchMemory(input)}
                      disabled={!input.trim()}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Search Memory
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">
                      Press Ctrl+Enter to submit
                    </p>
                    <Button 
                      onClick={handleSubmit} 
                      disabled={isLoading || !input.trim()}
                      className="min-w-[120px]"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Submit RFQ
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Results Tab - keeping existing result display */}
          <TabsContent value="results" className="space-y-4">
            {result && (
              <>
                {/* Status Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      RFQ Processing Complete
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">RFQ ID</p>
                        <p className="text-sm text-muted-foreground">{result.rfq_state.rfq_id}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Progress</p>
                        <div className="space-y-1">
                          <Progress value={result.rfq_state.progress_percentage} className="h-2" />
                          <p className="text-xs text-muted-foreground">{result.rfq_state.progress_percentage}% complete</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Vendors Contacted</p>
                        <p className="text-sm text-muted-foreground">{result.communication_results.total_sent}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">API Used</p>
                        <Badge variant="secondary">{result.system_info.api_used}</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Rest of existing result display... */}
                {/* Keep all the existing cards for market intelligence, vendors, etc. */}
              </>
            )}
          </TabsContent>

          {/* Workflow Control Tab */}
          <TabsContent value="workflow" className="space-y-4">
            {workflowStatus && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Workflow Control
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium">Execution ID</p>
                      <p className="text-sm text-muted-foreground font-mono">{workflowStatus.execution_id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Status</p>
                      <Badge variant={
                        workflowStatus.execution_state === "completed" ? "default" :
                        workflowStatus.execution_state === "running" ? "secondary" :
                        workflowStatus.execution_state === "failed" ? "destructive" : "outline"
                      }>
                        {workflowStatus.execution_state}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Current Step</p>
                      <p className="text-sm text-muted-foreground">{workflowStatus.current_step}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium">Progress</p>
                    <Progress value={workflowStatus.progress_percentage} className="h-3" />
                    <p className="text-xs text-muted-foreground">{workflowStatus.progress_percentage}% complete</p>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleWorkflowControl("pause")}
                      disabled={workflowStatus.execution_state !== "running"}
                    >
                      <Pause className="h-4 w-4 mr-1" />
                      Pause
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleWorkflowControl("resume")}
                      disabled={workflowStatus.execution_state !== "paused"}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      Resume
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (executionId) {
                          fetch(`${API_BASE}/api/workflows/${executionId}/status`)
                            .then(res => res.json())
                            .then(setWorkflowStatus);
                        }
                      }}
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Refresh
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Human Tasks Tab */}
          <TabsContent value="tasks" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Pending Human Tasks
                </CardTitle>
                <CardDescription>
                  Tasks requiring human approval or input
                </CardDescription>
              </CardHeader>
              <CardContent>
                {humanTasks.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">No pending tasks</p>
                ) : (
                  <div className="space-y-4">
                    {humanTasks.map((task) => (
                      <Card key={task.id} className="border-l-4 border-l-orange-500">
                        <CardHeader>
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle className="text-lg">{task.title}</CardTitle>
                              <CardDescription>{task.description}</CardDescription>
                            </div>
                            <Badge variant="outline">Priority {task.priority}</Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              onClick={() => handleTaskResponse(task.id, true)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleTaskResponse(task.id, false)}
                            >
                              <AlertTriangle className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Memory Search Tab */}
          <TabsContent value="memory" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Semantic Memory Search
                </CardTitle>
                <CardDescription>
                  Search through previous workflows and knowledge
                </CardDescription>
              </CardHeader>
              <CardContent>
                {memoryResults ? (
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      Found {memoryResults.total_found} results in {memoryResults.search_time}s
                    </div>
                    <div className="space-y-3">
                      {memoryResults.results.map((memory) => (
                        <Card key={memory.id} className="border-l-4 border-l-blue-500">
                          <CardContent className="pt-4">
                            <div className="flex justify-between items-start mb-2">
                              <Badge variant="outline">{memory.memory_type}</Badge>
                              <span className="text-sm text-muted-foreground">
                                {(memory.similarity * 100).toFixed(1)}% match
                              </span>
                            </div>
                            <p className="text-sm">{memory.content}</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {new Date(memory.created_at).toLocaleDateString()}
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground py-8">
                    Enter a search query in the RFQ submission tab and click "Search Memory"
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}