"""
RFQ Tools for structured workflow operations
Implements Factor 4: Tools Are Just Structured Outputs
"""

import structlog
import re
from typing import Dict, Any, List
from datetime import datetime, timedelta

logger = structlog.get_logger(__name__)

async def parse_rfq_request_tool(natural_language_request: str) -> Dict[str, Any]:
    """
    Parse natural language RFQ request into structured data
    Factor 1: Natural Language to Tool Calls
    """
    logger.info("Parsing RFQ request", request_length=len(natural_language_request))
    
    try:
        input_text = natural_language_request.lower()
        
        # Extract urgency
        urgency = "standard"
        urgency_keywords = {
            'urgent': 'high',
            'asap': 'critical', 
            'immediately': 'critical',
            'rush': 'high',
            'priority': 'high',
            'critical': 'critical'
        }
        
        for keyword, level in urgency_keywords.items():
            if keyword in input_text:
                urgency = level
                break
        
        # Extract department
        department = "General"
        department_keywords = {
            'it': 'IT',
            'technology': 'IT',
            'computer': 'IT',
            'laptop': 'IT',
            'software': 'IT',
            'hr': 'HR',
            'human resources': 'HR',
            'finance': 'Finance',
            'accounting': 'Finance',
            'marketing': 'Marketing',
            'sales': 'Sales',
            'operations': 'Operations',
            'facilities': 'Facilities'
        }
        
        for keyword, dept in department_keywords.items():
            if keyword in input_text:
                department = dept
                break
        
        # Extract budget
        budget_range = None
        budget_patterns = [
            r'\$[\d,]+(?:k|K|m|M)?',
            r'\d+(?:k|K|m|M)?\s*(?:dollars?|USD|INR|rupees?)',
            r'budget.*?\$?[\d,]+',
            r'around.*?\$?[\d,]+'
        ]
        
        for pattern in budget_patterns:
            match = re.search(pattern, natural_language_request, re.IGNORECASE)
            if match:
                budget_range = match.group(0)
                break
        
        # Extract region
        preferred_region = "India"
        region_keywords = [
            'india', 'bangalore', 'mumbai', 'delhi', 'chennai', 
            'hyderabad', 'pune', 'kolkata', 'mangaluru', 'gurgaon'
        ]
        
        for region in region_keywords:
            if region in input_text:
                preferred_region = region.title()
                break
        
        # Extract quantity
        quantity_match = re.search(r'(\d+)', natural_language_request)
        quantity = int(quantity_match.group(1)) if quantity_match else 1
        
        parsed_request = {
            "natural_language_request": natural_language_request,
            "requester_id": f"req_{int(datetime.utcnow().timestamp())}",
            "department": department,
            "urgency": urgency,
            "budget_range": budget_range,
            "preferred_region": preferred_region,
            "quantity": quantity,
            "parsed_at": datetime.utcnow().isoformat()
        }
        
        logger.info("RFQ request parsed successfully", 
                   department=department, 
                   urgency=urgency,
                   quantity=quantity)
        
        return parsed_request
        
    except Exception as e:
        logger.error("Failed to parse RFQ request", error=str(e))
        raise

async def conduct_market_research_tool(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Conduct structured market research for RFQ
    Factor 4: Structured output from market intelligence
    """
    logger.info("Conducting market research", 
               department=request.get("department"), 
               region=request.get("preferred_region"))
    
    try:
        item_keywords = request["natural_language_request"].lower()
        quantity = request.get("quantity", 1)
        
        # Determine item category and pricing
        if 'laptop' in item_keywords:
            price_range = f"${quantity * 800}-${quantity * 1200} for {quantity} business laptops"
            market_trends = "Laptop market showing stable pricing with 8-12% annual growth. Remote work driving demand for business laptops."
            supplier_recommendations = ["Dell Technologies", "HP Enterprise", "Lenovo Business Solutions"]
        elif 'desktop' in item_keywords:
            price_range = f"${quantity * 600}-${quantity * 1000} for {quantity} desktop computers"
            market_trends = "Desktop market showing 5-8% annual growth with enterprise focus on performance and security."
            supplier_recommendations = ["HP Business Solutions", "Dell Commercial", "Lenovo ThinkCentre"]
        elif 'server' in item_keywords:
            price_range = f"${quantity * 2000}-${quantity * 5000} for {quantity} entry-level servers"
            market_trends = "Server market growing 10-15% annually with cloud integration and edge computing trends."
            supplier_recommendations = ["Dell EMC", "HPE ProLiant", "IBM Power Systems"]
        elif 'furniture' in item_keywords:
            price_range = f"${quantity * 200}-${quantity * 500} for {quantity} office furniture items"
            market_trends = "Office furniture market recovering with hybrid work trends driving ergonomic solutions."
            supplier_recommendations = ["Steelcase India", "Herman Miller", "Godrej Interio"]
        else:
            price_range = f"${quantity * 100}-${quantity * 1000} for {quantity} items (estimate)"
            market_trends = "General procurement market showing steady growth with focus on sustainability."
            supplier_recommendations = ["Regional Supplier A", "Regional Supplier B", "Regional Supplier C"]
        
        market_intelligence = {
            "price_range": price_range,
            "market_trends": market_trends,
            "supplier_recommendations": supplier_recommendations,
            "risk_factors": [
                "Supply chain delays due to global logistics",
                "Price volatility in raw materials",
                "Quality variations between suppliers",
                "Currency fluctuation risks",
                "Regulatory compliance changes"
            ],
            "compliance_requirements": [
                "ISO 9001:2015 quality management",
                "Regional ESG compliance standards",
                "Data protection and privacy compliance",
                "Local regulatory requirements",
                "Environmental certifications"
            ],
            "research_timestamp": datetime.utcnow().isoformat(),
            "confidence_score": 0.8
        }
        
        logger.info("Market research completed", 
                   price_range=price_range,
                   suppliers_count=len(supplier_recommendations))
        
        return market_intelligence
        
    except Exception as e:
        logger.error("Market research failed", error=str(e))
        raise

async def discover_vendors_tool(request: Dict[str, Any], market_intelligence: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Discover vendors based on request and market intelligence
    Factor 4: Structured vendor data output
    """
    logger.info("Discovering vendors", 
               department=request.get("department"),
               region=request.get("preferred_region"))
    
    try:
        item_keywords = request["natural_language_request"].lower()
        timestamp = int(datetime.utcnow().timestamp())
        vendors = []
        
        if 'laptop' in item_keywords or 'computer' in item_keywords or 'it' in item_keywords:
            vendors = [
                {
                    "id": f"vendor_{timestamp}_1",
                    "name": "Premium IT Solutions Pvt Ltd",
                    "email": "<EMAIL>",
                    "rating": 4.8,
                    "specialties": ["Business Laptops", "Desktop Computers", "IT Equipment"],
                    "location": request.get("preferred_region", "India"),
                    "contact_info": {
                        "phone": "+91-80-12345678",
                        "website": "https://premiumit.com",
                        "contact_person": "Rajesh Kumar"
                    },
                    "performance_history": {
                        "orders_completed": 150,
                        "avg_delivery_time": "7-10 days",
                        "customer_satisfaction": 4.8
                    }
                },
                {
                    "id": f"vendor_{timestamp}_2",
                    "name": "Global Tech Corporation",
                    "email": "<EMAIL>",
                    "rating": 4.5,
                    "specialties": ["Enterprise Hardware", "Bulk IT Orders", "Technical Support"],
                    "location": "Bangalore",
                    "contact_info": {
                        "phone": "+91-80-87654321",
                        "website": "https://globaltech.com",
                        "contact_person": "Priya Sharma"
                    },
                    "performance_history": {
                        "orders_completed": 200,
                        "avg_delivery_time": "5-8 days",
                        "customer_satisfaction": 4.5
                    }
                },
                {
                    "id": f"vendor_{timestamp}_3",
                    "name": "Regional IT Supplier",
                    "email": "<EMAIL>",
                    "rating": 4.2,
                    "specialties": ["Local IT Support", "Hardware Procurement", "Installation Services"],
                    "location": request.get("preferred_region", "India"),
                    "contact_info": {
                        "phone": "+91-80-11223344",
                        "website": "https://regionalit.com",
                        "contact_person": "Amit Patel"
                    },
                    "performance_history": {
                        "orders_completed": 80,
                        "avg_delivery_time": "10-12 days",
                        "customer_satisfaction": 4.2
                    }
                }
            ]
        elif 'furniture' in item_keywords or 'office' in item_keywords:
            vendors = [
                {
                    "id": f"vendor_{timestamp}_1",
                    "name": "Regional Office Suppliers Ltd",
                    "email": "<EMAIL>",
                    "rating": 4.2,
                    "specialties": ["Office Furniture", "Workspace Solutions", "Interior Design"],
                    "location": request.get("preferred_region", "India"),
                    "contact_info": {
                        "phone": "+91-80-55667788",
                        "website": "https://regionaloffice.com",
                        "contact_person": "Sneha Reddy"
                    },
                    "performance_history": {
                        "orders_completed": 120,
                        "avg_delivery_time": "14-21 days",
                        "customer_satisfaction": 4.2
                    }
                },
                {
                    "id": f"vendor_{timestamp}_2",
                    "name": "Premium Furniture Solutions",
                    "email": "<EMAIL>",
                    "rating": 4.6,
                    "specialties": ["Executive Furniture", "Ergonomic Solutions", "Custom Design"],
                    "location": "Mumbai",
                    "contact_info": {
                        "phone": "+91-22-99887766",
                        "website": "https://premiumfurniture.com",
                        "contact_person": "Ravi Gupta"
                    },
                    "performance_history": {
                        "orders_completed": 90,
                        "avg_delivery_time": "21-28 days",
                        "customer_satisfaction": 4.6
                    }
                },
                {
                    "id": f"vendor_{timestamp}_3",
                    "name": "Workspace Innovations",
                    "email": "<EMAIL>",
                    "rating": 4.3,
                    "specialties": ["Modern Office Furniture", "Space Planning", "Installation"],
                    "location": request.get("preferred_region", "India"),
                    "contact_info": {
                        "phone": "+91-80-44556677",
                        "website": "https://workspaceinnovations.com",
                        "contact_person": "Lakshmi Iyer"
                    },
                    "performance_history": {
                        "orders_completed": 75,
                        "avg_delivery_time": "18-25 days",
                        "customer_satisfaction": 4.3
                    }
                }
            ]
        else:
            # General vendors
            vendors = [
                {
                    "id": f"vendor_{timestamp}_1",
                    "name": "Universal Procurement Solutions",
                    "email": "<EMAIL>",
                    "rating": 4.4,
                    "specialties": ["General Procurement", "Multi-category Sourcing", "Logistics"],
                    "location": request.get("preferred_region", "India"),
                    "contact_info": {
                        "phone": "+91-80-22334455",
                        "website": "https://universalprocurement.com",
                        "contact_person": "Suresh Kumar"
                    },
                    "performance_history": {
                        "orders_completed": 300,
                        "avg_delivery_time": "10-15 days",
                        "customer_satisfaction": 4.4
                    }
                },
                {
                    "id": f"vendor_{timestamp}_2",
                    "name": "Regional Supply Chain Ltd",
                    "email": "<EMAIL>",
                    "rating": 4.1,
                    "specialties": ["Local Sourcing", "Supply Chain Management", "Quality Assurance"],
                    "location": request.get("preferred_region", "India"),
                    "contact_info": {
                        "phone": "+91-80-33445566",
                        "website": "https://regionalsupply.com",
                        "contact_person": "Meera Singh"
                    },
                    "performance_history": {
                        "orders_completed": 180,
                        "avg_delivery_time": "12-18 days",
                        "customer_satisfaction": 4.1
                    }
                },
                {
                    "id": f"vendor_{timestamp}_3",
                    "name": "Enterprise Solutions Provider",
                    "email": "<EMAIL>",
                    "rating": 4.5,
                    "specialties": ["Enterprise Procurement", "Vendor Management", "Cost Optimization"],
                    "location": "Bangalore",
                    "contact_info": {
                        "phone": "+91-80-66778899",
                        "website": "https://enterprisesolutions.com",
                        "contact_person": "Vikram Agarwal"
                    },
                    "performance_history": {
                        "orders_completed": 250,
                        "avg_delivery_time": "8-12 days",
                        "customer_satisfaction": 4.5
                    }
                }
            ]
        
        logger.info("Vendors discovered successfully", 
                   vendor_count=len(vendors),
                   avg_rating=sum(v["rating"] for v in vendors) / len(vendors))
        
        return vendors
        
    except Exception as e:
        logger.error("Vendor discovery failed", error=str(e))
        raise

async def generate_rfq_document_tool(
    request: Dict[str, Any], 
    vendors: List[Dict[str, Any]], 
    market_intelligence: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Generate structured RFQ document
    Factor 4: Structured document output
    """
    logger.info("Generating RFQ document", 
               vendor_count=len(vendors),
               department=request.get("department"))
    
    try:
        quantity = request.get("quantity", 1)
        deadline = datetime.utcnow() + timedelta(days=14)
        
        item_keywords = request["natural_language_request"].lower()
        
        # Generate specifications based on item type
        if 'laptop' in item_keywords:
            specifications = [
                "Business-grade laptops with minimum 8GB RAM",
                "Intel Core i5 or AMD Ryzen 5 processor minimum",
                "256GB SSD storage minimum",
                "14-15 inch display with Full HD resolution",
                "Windows 11 Pro operating system",
                "Minimum 3-year warranty with on-site support",
                "Energy Star certification preferred"
            ]
        elif 'desktop' in item_keywords:
            specifications = [
                "Business desktop computers with minimum 8GB RAM",
                "Intel Core i5 or AMD Ryzen 5 processor minimum",
                "256GB SSD + 1TB HDD storage",
                "Integrated graphics or dedicated GPU as required",
                "Windows 11 Pro operating system",
                "Keyboard, mouse, and monitor included",
                "3-year comprehensive warranty"
            ]
        elif 'furniture' in item_keywords:
            specifications = [
                "Ergonomic design meeting international standards",
                "Durable materials with minimum 5-year warranty",
                "Assembly and installation services included",
                "Compliance with safety and environmental standards",
                "Color and finish as per office requirements",
                "Delivery and setup within specified timeline",
                "GREENGUARD certification preferred"
            ]
        else:
            specifications = [
                "Items must meet specified quality standards",
                "Compliance with all applicable regulations",
                "Warranty and support as per industry standards",
                "Delivery and installation services included",
                "Documentation and training materials provided",
                "Performance guarantees and service level agreements"
            ]
        
        rfq_document = {
            "title": f"RFQ - {request['natural_language_request'][:60]}...",
            "description": f"""Request for Quotation for {request['natural_language_request']}. 
This procurement is for {request['department']} department with {request['urgency']} priority. 
{f"Budget range: {request['budget_range']}. " if request.get('budget_range') else ''}
Delivery required in {request['preferred_region']}.

Market Context:
- {market_intelligence.get('price_range', 'Price analysis available')}
- {market_intelligence.get('market_trends', 'Market trends considered')}

Please provide comprehensive quotes including all costs, delivery timelines, and terms.""",
            "specifications": specifications,
            "quantity": quantity,
            "delivery_requirements": f"""
Delivery Location: {request['preferred_region']}
Delivery Terms: FOB destination
Installation: Required (if applicable)
Timeline: 2-4 weeks from order confirmation
Packaging: Secure packaging for safe delivery
Insurance: Full coverage during transit
            """.strip(),
            "evaluation_criteria": [
                "Price competitiveness (40%)",
                "Technical compliance and quality (30%)",
                "Delivery timeline and reliability (20%)",
                "Vendor reputation and support (10%)"
            ],
            "submission_deadline": deadline.strftime("%Y-%m-%d"),
            "terms_and_conditions": f"""
1. Quote Validity: 30 days from submission
2. Payment Terms: Net 30 days from delivery
3. Warranty: As specified in individual specifications
4. Compliance: All local regulations and standards must be met
5. Changes: Any specification changes must be approved in writing
6. Cancellation: Standard cancellation terms apply
7. Confidentiality: All information is confidential
8. Award: Decision will be communicated within 5 business days of deadline

Contact Information:
Department: {request['department']}
Urgency Level: {request['urgency']}
Reference: {request['requester_id']}
            """.strip(),
            "generated_at": datetime.utcnow().isoformat(),
            "rfq_id": f"RFQ-{request['department']}-{datetime.utcnow().strftime('%Y%m%d')}-{request['requester_id'][-6:]}"
        }
        
        logger.info("RFQ document generated successfully", 
                   rfq_id=rfq_document["rfq_id"],
                   specifications_count=len(specifications))
        
        return rfq_document
        
    except Exception as e:
        logger.error("RFQ document generation failed", error=str(e))
        raise

async def send_rfq_to_vendors_tool(rfq_document: Dict[str, Any], vendors: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Simulate sending RFQ to vendors
    Factor 4: Structured communication results
    """
    logger.info("Sending RFQ to vendors", 
               vendor_count=len(vendors),
               rfq_id=rfq_document.get("rfq_id"))
    
    try:
        delivery_results = []
        successful_deliveries = 0
        failed_deliveries = 0
        
        for vendor in vendors:
            # Simulate delivery (in real implementation, this would send emails)
            delivery_success = True  # Assume success for simulation
            
            result = {
                "vendor_email": vendor["email"],
                "vendor_name": vendor["name"],
                "vendor_id": vendor["id"],
                "status": "sent" if delivery_success else "failed",
                "sent_at": datetime.utcnow().isoformat(),
                "tracking_id": f"rfq_{datetime.utcnow().timestamp()}_{hash(vendor['id']) % 10000}",
                "delivery_method": "email",
                "content_type": "rfq_document_pdf"
            }
            
            if delivery_success:
                successful_deliveries += 1
            else:
                failed_deliveries += 1
                result["error_reason"] = "Email delivery failed"
            
            delivery_results.append(result)
        
        communication_results = {
            "total_sent": len(vendors),
            "successful_deliveries": successful_deliveries,
            "failed_deliveries": failed_deliveries,
            "delivery_results": delivery_results,
            "rfq_title": rfq_document["title"],
            "submission_deadline": rfq_document["submission_deadline"],
            "communication_timestamp": datetime.utcnow().isoformat(),
            "follow_up_scheduled": (datetime.utcnow() + timedelta(days=3)).isoformat(),
            "expected_responses": successful_deliveries,
            "success_rate": successful_deliveries / len(vendors) if vendors else 0
        }
        
        logger.info("RFQ sent to vendors successfully", 
                   successful_deliveries=successful_deliveries,
                   failed_deliveries=failed_deliveries,
                   success_rate=communication_results["success_rate"])
        
        return communication_results
        
    except Exception as e:
        logger.error("Failed to send RFQ to vendors", error=str(e))
        raise