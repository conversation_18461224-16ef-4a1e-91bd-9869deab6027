"""
Database connection and management module
Implements PostgreSQL with pgvector for AI agent memory system
"""

import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.orm import declarative_base
import structlog
from typing import Optional, AsyncGenerator
from contextlib import asynccontextmanager
import asyncio

from .config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

# SQLAlchemy base
Base = declarative_base()

# Global database engine and session maker
_engine: Optional[object] = None
_async_session_maker: Optional[async_sessionmaker] = None
_connection_pool: Optional[asyncpg.Pool] = None

async def init_database():
    """Initialize database engine and connection pool"""
    global _engine, _async_session_maker, _connection_pool
    
    try:
        # Create SQLAlchemy engine for ORM operations
        _engine = create_async_engine(
            settings.database_url.replace("postgresql://", "postgresql+asyncpg://"),
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
            echo=settings.is_development,
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections every hour
        )
        
        # Create session maker
        _async_session_maker = async_sessionmaker(
            _engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Create asyncpg connection pool for raw SQL operations
        _connection_pool = await asyncpg.create_pool(
            settings.database_url,
            min_size=5,
            max_size=settings.database_pool_size,
            command_timeout=60,
            server_settings={
                'jit': 'off',  # Disable JIT for better performance with pgvector
                'application_name': 'ai_agent_service'
            }
        )
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        raise

async def close_database():
    """Close database connections"""
    global _engine, _connection_pool
    
    if _connection_pool:
        await _connection_pool.close()
        logger.info("Database connection pool closed")
    
    if _engine:
        await _engine.dispose()
        logger.info("Database engine disposed")

@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session with automatic cleanup"""
    if not _async_session_maker:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    async with _async_session_maker() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

async def get_database():
    """Get database connection from pool"""
    if not _connection_pool:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    return _connection_pool

class DatabaseService:
    """Database service class for complex operations"""
    
    def __init__(self):
        self.pool = None
    
    async def execute_query(self, query: str, params: dict = None):
        """Execute a query with parameters"""
        pool = await get_database()
        async with pool.acquire() as connection:
            if params:
                return await connection.fetch(query, **params)
            else:
                return await connection.fetch(query)
    
    async def execute_single_query(self, query: str, params: dict = None):
        """Execute a query and return single result"""
        pool = await get_database()
        async with pool.acquire() as connection:
            if params:
                return await connection.fetchrow(query, **params)
            else:
                return await connection.fetchrow(query)
    
    async def execute_command(self, command: str, params: dict = None):
        """Execute a command (INSERT, UPDATE, DELETE)"""
        pool = await get_database()
        async with pool.acquire() as connection:
            if params:
                return await connection.execute(command, **params)
            else:
                return await connection.execute(command)
    
    async def execute_transaction(self, commands: list):
        """Execute multiple commands in a transaction"""
        pool = await get_database()
        async with pool.acquire() as connection:
            async with connection.transaction():
                results = []
                for command, params in commands:
                    if params:
                        result = await connection.execute(command, **params)
                    else:
                        result = await connection.execute(command)
                    results.append(result)
                return results

# Vector similarity search utilities
class VectorSearch:
    """Utilities for pgvector similarity search"""
    
    @staticmethod
    async def similarity_search(
        table: str,
        embedding_column: str,
        query_embedding: list,
        limit: int = 10,
        similarity_threshold: float = 0.7
    ):
        """Perform vector similarity search"""
        pool = await get_database()
        
        query = f"""
        SELECT *, 1 - ({embedding_column} <=> $1::vector) AS similarity
        FROM {table}
        WHERE 1 - ({embedding_column} <=> $1::vector) > $2
        ORDER BY {embedding_column} <=> $1::vector
        LIMIT $3
        """
        
        async with pool.acquire() as connection:
            return await connection.fetch(
                query, 
                query_embedding, 
                similarity_threshold, 
                limit
            )
    
    @staticmethod
    async def hybrid_search(
        table: str,
        embedding_column: str,
        text_column: str,
        query_embedding: list,
        query_text: str,
        limit: int = 10,
        vector_weight: float = 0.7,
        text_weight: float = 0.3
    ):
        """Perform hybrid vector + text search"""
        pool = await get_database()
        
        query = f"""
        SELECT *, 
               ($4 * (1 - ({embedding_column} <=> $1::vector))) + 
               ($5 * ts_rank_cd(to_tsvector('english', {text_column}), plainto_tsquery('english', $2))) 
               AS combined_score
        FROM {table}
        WHERE to_tsvector('english', {text_column}) @@ plainto_tsquery('english', $2)
           OR 1 - ({embedding_column} <=> $1::vector) > 0.5
        ORDER BY combined_score DESC
        LIMIT $3
        """
        
        async with pool.acquire() as connection:
            return await connection.fetch(
                query,
                query_embedding,
                query_text,
                limit,
                vector_weight,
                text_weight
            )

# Memory-specific database operations
class MemoryDatabase:
    """Database operations for memory management"""
    
    @staticmethod
    async def store_memory(
        content: str,
        embedding: list,
        memory_type: str,
        metadata: dict,
        importance_score: float = 0.5
    ):
        """Store a memory with embedding"""
        pool = await get_database()
        
        query = """
        INSERT INTO agent_memory.memories 
        (content, embedding, memory_type, metadata, importance_score)
        VALUES ($1, $2::vector, $3, $4, $5)
        RETURNING id
        """
        
        async with pool.acquire() as connection:
            return await connection.fetchval(
                query,
                content,
                embedding,
                memory_type,
                metadata,
                importance_score
            )
    
    @staticmethod
    async def search_memories(
        query_embedding: list,
        limit: int = 10,
        memory_type: str = None,
        importance_threshold: float = 0.3
    ):
        """Search memories by embedding similarity"""
        pool = await get_database()
        
        conditions = ["importance_score >= $3"]
        params = [query_embedding, limit, importance_threshold]
        
        if memory_type:
            conditions.append("memory_type = $4")
            params.append(memory_type)
        
        query = f"""
        SELECT id, content, metadata, memory_type, importance_score,
               1 - (embedding <=> $1::vector) AS similarity,
               access_count, last_accessed, created_at
        FROM agent_memory.memories
        WHERE {' AND '.join(conditions)}
        ORDER BY embedding <=> $1::vector
        LIMIT $2
        """
        
        async with pool.acquire() as connection:
            return await connection.fetch(query, *params)
    
    @staticmethod
    async def update_memory_access(memory_id: str):
        """Update memory access tracking"""
        pool = await get_database()
        
        query = """
        UPDATE agent_memory.memories 
        SET access_count = access_count + 1, last_accessed = NOW()
        WHERE id = $1
        """
        
        async with pool.acquire() as connection:
            await connection.execute(query, memory_id)
    
    @staticmethod
    async def consolidate_memories(batch_size: int = 100):
        """Consolidate and cluster memories"""
        pool = await get_database()
        
        # Get memories that need consolidation
        query = """
        SELECT id, content, embedding, metadata, importance_score
        FROM agent_memory.memories
        WHERE last_accessed > NOW() - INTERVAL '24 hours'
        ORDER BY importance_score DESC, access_count DESC
        LIMIT $1
        """
        
        async with pool.acquire() as connection:
            memories = await connection.fetch(query, batch_size)
            
            # Simple clustering logic - group similar memories
            # This would be enhanced with more sophisticated clustering algorithms
            if len(memories) > 1:
                # Update memory clusters
                cluster_query = """
                INSERT INTO agent_memory.memory_clusters 
                (name, description, memory_ids, cluster_metadata)
                VALUES ($1, $2, $3, $4)
                """
                
                memory_ids = [str(m['id']) for m in memories]
                await connection.execute(
                    cluster_query,
                    f"Auto-cluster-{len(memory_ids)}",
                    f"Automatically generated cluster of {len(memory_ids)} related memories",
                    memory_ids,
                    {"auto_generated": True, "size": len(memory_ids)}
                )
        
        logger.info(f"Consolidated {len(memories)} memories")
        return len(memories)

# Export database service instance
db_service = DatabaseService()
vector_search = VectorSearch()
memory_db = MemoryDatabase()