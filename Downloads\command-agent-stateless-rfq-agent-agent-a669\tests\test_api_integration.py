"""
API Integration Tests for LangGraph AI Agent System
Tests all API endpoints and integration points
"""

import pytest
import json
from httpx import AsyncClient
from unittest.mock import patch
from .conftest import TestBase


class TestAPIIntegration(TestBase):
    """API integration tests"""
    
    @pytest.fixture
    async def api_client(self):
        """Create test API client"""
        from services.agent.main import app
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    # ========== HEALTH AND STATUS ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_health_endpoint(self, api_client):
        """Test health check endpoint"""
        response = await api_client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "services" in data
    
    @pytest.mark.integration  
    async def test_readiness_endpoint(self, api_client):
        """Test readiness check endpoint"""
        response = await api_client.get("/ready")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "ready"
        assert "timestamp" in data
    
    @pytest.mark.integration
    async def test_metrics_endpoint(self, api_client):
        """Test metrics endpoint"""
        response = await api_client.get("/metrics")
        assert response.status_code == 200
        
        # Should return Prometheus format
        content = response.text
        assert "# HELP" in content or "# TYPE" in content
    
    # ========== MAIN RFQ WORKFLOW ENDPOINT ==========
    
    @pytest.mark.integration
    async def test_rfq_processing_endpoint(self, api_client):
        """Test main RFQ processing endpoint"""
        rfq_data = {
            "request_id": "test_rfq_001",
            "user_id": "test_user",
            "natural_language_request": "Need 20 laptops for Mumbai office, budget $25,000",
            "department": "IT",
            "urgency": "medium",
            "preferred_region": "India"
        }
        
        response = await api_client.post("/api/rfq/process", json=rfq_data)
        assert response.status_code == 200
        
        data = response.json()
        self.assert_successful_response(data)
        assert "execution_id" in data
        assert "rfq_state" in data
    
    @pytest.mark.integration
    async def test_rfq_processing_validation(self, api_client):
        """Test RFQ processing input validation"""
        # Missing required fields
        invalid_data = {
            "user_id": "test_user"
            # Missing request_id and natural_language_request
        }
        
        response = await api_client.post("/api/rfq/process", json=invalid_data)
        assert response.status_code == 422  # Validation error
    
    # ========== WORKFLOW CONTROL ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_workflow_control_endpoints(self, api_client):
        """Test workflow pause/resume/status endpoints"""
        execution_id = "test_execution_123"
        
        # Test workflow status
        response = await api_client.get(f"/api/workflows/{execution_id}/status")
        # Should return 404 for non-existent workflow
        assert response.status_code == 404
        
        # Test pause endpoint
        response = await api_client.post(f"/api/workflows/{execution_id}/pause")
        # May fail if workflow doesn't exist, but endpoint should be accessible
        assert response.status_code in [200, 404, 500]
        
        # Test resume endpoint
        response = await api_client.post(f"/api/workflows/{execution_id}/resume")
        assert response.status_code in [200, 404, 500]
    
    # ========== HUMAN TASK ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_human_tasks_endpoints(self, api_client):
        """Test human-in-the-loop endpoints"""
        
        # Test get pending tasks
        response = await api_client.get("/api/human-tasks")
        assert response.status_code == 200
        
        data = response.json()
        assert "tasks" in data
        assert isinstance(data["tasks"], list)
        
        # Test get tasks for specific user
        response = await api_client.get("/api/human-tasks?user_id=test_user")
        assert response.status_code == 200
    
    @pytest.mark.integration
    async def test_human_task_response(self, api_client):
        """Test human task response endpoint"""
        task_id = "test_task_123"
        response_data = {
            "approved": True,
            "comments": "Approved for testing",
            "additional_data": {"budget_approved": 25000}
        }
        
        response = await api_client.post(f"/api/human-tasks/{task_id}/respond", json=response_data)
        # May return 404 if task doesn't exist
        assert response.status_code in [200, 404]
    
    # ========== MEMORY ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_memory_search_endpoint(self, api_client):
        """Test memory search endpoint"""
        search_params = {
            "query": "laptop procurement India",
            "limit": 5
        }
        
        response = await api_client.post("/api/memory/search", params=search_params)
        assert response.status_code == 200
        
        data = response.json()
        assert "results" in data
        assert isinstance(data["results"], list)
    
    @pytest.mark.integration
    async def test_memory_consolidation_endpoint(self, api_client):
        """Test memory consolidation endpoint"""
        response = await api_client.post("/api/memory/consolidate")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "consolidation_scheduled"
    
    # ========== PROMPT MANAGEMENT ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_prompt_management_endpoints(self, api_client):
        """Test prompt management endpoints"""
        
        # Test get prompts
        response = await api_client.get("/api/prompts")
        assert response.status_code == 200
        
        data = response.json()
        assert "prompts" in data
        
        # Test create prompt
        prompt_data = {
            "name": "test_prompt",
            "template": "Research {topic} in {region}",
            "version": "1.0.0",
            "variables": ["topic", "region"]
        }
        
        response = await api_client.post("/api/prompts", json=prompt_data)
        assert response.status_code == 200
    
    # ========== TOOL REGISTRY ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_tool_registry_endpoint(self, api_client):
        """Test tool registry endpoint"""
        response = await api_client.get("/api/tools")
        assert response.status_code == 200
        
        data = response.json()
        assert "tools" in data
        assert isinstance(data["tools"], list)
    
    # ========== UNIVERSAL INPUT GATEWAY ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_universal_input_text_endpoint(self, api_client):
        """Test universal input text endpoint"""
        input_data = {
            "text": "Need 15 office chairs for Bangalore office",
            "source": "api",
            "priority": "medium"
        }
        
        response = await api_client.post("/api/input/text", json=input_data)
        assert response.status_code == 200
        
        data = response.json()
        self.assert_successful_response(data)
        assert data["input_processed"] is True
        assert "intent" in data
    
    @pytest.mark.integration
    async def test_universal_input_json_endpoint(self, api_client):
        """Test universal input JSON endpoint"""
        input_data = {
            "data": {
                "message": "Procurement request for conference room setup",
                "budget": "$15,000",
                "urgency": "high"
            },
            "source": "chat"
        }
        
        response = await api_client.post("/api/input/json", json=input_data)
        assert response.status_code == 200
        
        data = response.json()
        self.assert_successful_response(data)
        assert data["source"] == "chat"
    
    @pytest.mark.integration
    async def test_universal_input_email_endpoint(self, api_client):
        """Test universal input email endpoint"""
        email_data = {
            "subject": "RFQ Request - Office Supplies",
            "body": "We need office supplies for our new branch. Please send quotes.",
            "sender_email": "<EMAIL>",
            "priority": "medium"
        }
        
        response = await api_client.post("/api/input/email", json=email_data)
        assert response.status_code == 200
        
        data = response.json()
        self.assert_successful_response(data)
        assert data["source"] == "email"
    
    @pytest.mark.integration
    async def test_universal_input_chat_endpoint(self, api_client):
        """Test universal input chat endpoint"""
        chat_data = {
            "message": "Can you help me procure 10 monitors for the design team?",
            "user_id": "designer_001",
            "channel_id": "procurement_channel"
        }
        
        response = await api_client.post("/api/input/chat", json=chat_data)
        assert response.status_code == 200
        
        data = response.json()
        self.assert_successful_response(data)
        assert data["source"] == "chat"
    
    @pytest.mark.integration
    async def test_input_sources_endpoint(self, api_client):
        """Test input sources capability endpoint"""
        response = await api_client.get("/api/input/sources")
        assert response.status_code == 200
        
        data = response.json()
        assert "supported_sources" in data
        assert "supported_formats" in data
        assert "endpoints" in data
        assert "capabilities" in data
    
    # ========== FUNCTIONAL WORKFLOW ENDPOINTS ==========
    
    @pytest.mark.integration
    async def test_functional_rfq_endpoint(self, api_client):
        """Test functional RFQ workflow endpoint"""
        request_data = {
            "rfq_request": "Need 5 projectors for training rooms",
            "user_id": "training_manager"
        }
        
        response = await api_client.post("/api/functional/rfq", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        self.assert_successful_response(data)
        assert "execution_id" in data
        assert "final_status" in data
    
    @pytest.mark.integration
    async def test_parallel_functional_workflow_endpoint(self, api_client):
        """Test parallel functional workflow endpoint"""
        request_data = {
            "rfq_request": "Procurement for complete office setup including furniture and IT equipment"
        }
        
        response = await api_client.post("/api/functional/parallel-rfq", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        self.assert_successful_response(data)
        assert "parallel_results" in data
        assert "research" in data["parallel_results"]
        assert "vendor" in data["parallel_results"]
        assert "document" in data["parallel_results"]
    
    @pytest.mark.integration
    async def test_functional_capabilities_endpoint(self, api_client):
        """Test functional capabilities endpoint"""
        response = await api_client.get("/api/functional/capabilities")
        assert response.status_code == 200
        
        data = response.json()
        assert "stateless_reducer" in data
        assert "functional_workflow" in data
        assert "factor_compliance" in data
        assert "functional_principles" in data
    
    # ========== ERROR HANDLING TESTS ==========
    
    @pytest.mark.integration
    async def test_api_error_handling(self, api_client):
        """Test API error handling"""
        
        # Test invalid JSON
        response = await api_client.post(
            "/api/rfq/process", 
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
        
        # Test invalid endpoint
        response = await api_client.get("/api/nonexistent")
        assert response.status_code == 404
        
        # Test method not allowed
        response = await api_client.delete("/api/rfq/process")
        assert response.status_code == 405
    
    # ========== PERFORMANCE TESTS ==========
    
    @pytest.mark.integration
    @pytest.mark.slow
    async def test_api_performance_basic(self, api_client):
        """Test basic API performance"""
        import time
        
        # Test health endpoint performance
        start_time = time.time()
        response = await api_client.get("/health")
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 1.0  # Should respond within 1 second
        
        # Test simple input processing performance
        input_data = {
            "text": "Quick test message",
            "source": "api"
        }
        
        start_time = time.time()
        response = await api_client.post("/api/input/text", json=input_data)
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 5.0  # Should process within 5 seconds
    
    # ========== SECURITY TESTS ==========
    
    @pytest.mark.integration
    async def test_api_security_headers(self, api_client):
        """Test API security headers"""
        response = await api_client.get("/health")
        
        # Check for security headers
        headers = response.headers
        assert "x-content-type-options" in headers
        # Add more security header checks as needed
    
    @pytest.mark.integration
    async def test_api_input_sanitization(self, api_client):
        """Test API input sanitization"""
        
        # Test with potentially malicious input
        malicious_input = {
            "text": "<script>alert('xss')</script>",
            "source": "api"
        }
        
        response = await api_client.post("/api/input/text", json=malicious_input)
        assert response.status_code == 200
        
        # Response should not contain the script tag
        data = response.json()
        response_text = json.dumps(data)
        assert "<script>" not in response_text
    
    # ========== RATE LIMITING TESTS ==========
    
    @pytest.mark.integration
    async def test_rate_limiting(self, api_client):
        """Test API rate limiting"""
        
        # Make multiple rapid requests
        responses = []
        for i in range(10):
            response = await api_client.get("/health")
            responses.append(response)
        
        # All should succeed for health endpoint (typically not rate limited)
        assert all(r.status_code == 200 for r in responses)
        
        # For processing endpoints, rate limiting should apply
        # (This would need actual rate limiting configuration to test properly)
    
    # ========== INTEGRATION WITH EXTERNAL SERVICES ==========
    
    @pytest.mark.integration
    async def test_database_integration(self, api_client):
        """Test database integration through API"""
        
        # Test memory search which uses database
        response = await api_client.post("/api/memory/search", params={"query": "test"})
        assert response.status_code == 200
        
        # Test prompt management which uses database
        response = await api_client.get("/api/prompts")
        assert response.status_code == 200
    
    @pytest.mark.integration
    async def test_redis_integration(self, api_client):
        """Test Redis integration through API"""
        
        # Test endpoints that would use Redis caching
        response = await api_client.get("/health")
        assert response.status_code == 200
        
        # Multiple calls should be served efficiently (cached)
        for _ in range(3):
            response = await api_client.get("/health")
            assert response.status_code == 200